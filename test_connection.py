#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连接脚本 - 验证是否能够连接到目标服务器
"""

import requests
from config import SERVER_CONFIG, AUTH_CONFIG, REQUEST_CONFIG

def test_server_connection():
    """测试服务器连接"""
    print("=" * 50)
    print("测试服务器连接")
    print("=" * 50)
    
    try:
        # 测试基础连接
        print(f"正在测试连接到: {SERVER_CONFIG['base_url']}")
        response = requests.get(
            SERVER_CONFIG['base_url'], 
            timeout=REQUEST_CONFIG['timeout'],
            headers=REQUEST_CONFIG['headers']
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 服务器连接成功！")
        else:
            print(f"⚠️  服务器响应状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：无法连接到服务器")
        print("请检查：")
        print("1. 服务器地址是否正确")
        print("2. 网络连接是否正常")
        print("3. 防火墙设置")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        return False
    
    return True

def test_login_page():
    """测试登录页面"""
    print("\n" + "=" * 50)
    print("测试登录页面")
    print("=" * 50)
    
    try:
        print(f"正在访问登录页面: {SERVER_CONFIG['login_url']}")
        response = requests.get(
            SERVER_CONFIG['login_url'],
            timeout=REQUEST_CONFIG['timeout'],
            headers=REQUEST_CONFIG['headers']
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 登录页面访问成功！")
            
            # 检查页面内容
            content = response.text.lower()
            if 'login' in content or 'username' in content or 'password' in content:
                print("✅ 页面包含登录相关内容")
            else:
                print("⚠️  页面可能不是登录页面")
                
            print(f"页面大小: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ 登录页面访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 访问登录页面时发生错误: {str(e)}")
        return False

def test_data_pages():
    """测试数据页面"""
    print("\n" + "=" * 50)
    print("测试数据页面")
    print("=" * 50)
    
    for url in SERVER_CONFIG['data_urls']:
        try:
            print(f"\n正在测试: {url}")
            response = requests.get(
                url,
                timeout=REQUEST_CONFIG['timeout'],
                headers=REQUEST_CONFIG['headers']
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 页面访问成功")
                print(f"页面大小: {len(response.text)} 字符")
            elif response.status_code == 401:
                print("🔐 需要登录认证")
            elif response.status_code == 403:
                print("🚫 访问被禁止")
            elif response.status_code == 404:
                print("❌ 页面不存在")
            else:
                print(f"⚠️  状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 访问页面时发生错误: {str(e)}")

def main():
    """主函数"""
    print("Web Scraper 连接测试工具")
    print(f"目标服务器: {SERVER_CONFIG['base_url']}")
    print(f"用户名: {AUTH_CONFIG['username']}")
    print()
    
    # 测试服务器连接
    if test_server_connection():
        # 测试登录页面
        test_login_page()
        
        # 测试数据页面
        test_data_pages()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
