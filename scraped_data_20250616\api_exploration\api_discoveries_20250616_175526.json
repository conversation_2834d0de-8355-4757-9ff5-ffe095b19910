[{"endpoint": "/api/substrate/overall", "url": "http://************:31539/api/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:43 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:37.931125", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/data", "url": "http://************:31539/api/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:44 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:38.577935", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/summary", "url": "http://************:31539/api/substrate/summary", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:44 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:39.193820", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/stations", "url": "http://************:31539/api/substrate/stations", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:45 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:39.808548", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/statistics", "url": "http://************:31539/api/substrate/statistics", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:45 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:40.415353", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/substrate", "url": "http://************:31539/api/data/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:46 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:41.067432", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/overall", "url": "http://************:31539/api/data/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:47 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:41.672770", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/stations", "url": "http://************:31539/api/data/stations", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:47 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:42.279467", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/dashboard/data", "url": "http://************:31539/api/dashboard/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:48 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:42.903310", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/dashboard/substrate", "url": "http://************:31539/api/dashboard/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:49 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:43.527761", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/statistics", "url": "http://************:31539/api/statistics", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:49 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:44.159705", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/reports", "url": "http://************:31539/api/reports", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:50 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:44.825550", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/export", "url": "http://************:31539/api/export", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:51 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:45.475843", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/overall", "url": "http://************:31539/substrate/api/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:51 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:46.157693", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/data", "url": "http://************:31539/substrate/api/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:52 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:46.832771", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/summary", "url": "http://************:31539/substrate/api/summary", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:53 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:47.474186", "content_type": "text/html", "is_json": false}, {"endpoint": "/data/api/substrate", "url": "http://************:31539/data/api/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:53 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:48.118000", "content_type": "text/html", "is_json": false}, {"endpoint": "/data/api/overall", "url": "http://************:31539/data/api/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:54 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:48.789290", "content_type": "text/html", "is_json": false}, {"endpoint": "/backend/api/substrate", "url": "http://************:31539/backend/api/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:54 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:49.432455", "content_type": "text/html", "is_json": false}, {"endpoint": "/backend/api/data", "url": "http://************:31539/backend/api/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:55 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:50.045881", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/substrate/overall", "url": "http://************:31539/v1/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:56 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:50.703067", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/substrate/data", "url": "http://************:31539/v1/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:56 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:51.356743", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/data/substrate", "url": "http://************:31539/v1/data/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:57 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:51.978008", "content_type": "text/html", "is_json": false}, {"endpoint": "/v2/substrate/overall", "url": "http://************:31539/v2/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:52.605707", "content_type": "text/html", "is_json": false}, {"endpoint": "/v2/substrate/data", "url": "http://************:31539/v2/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:55:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:54:53.213991", "content_type": "text/html", "is_json": false}]