
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>進階Substrate爬蟲報告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .result { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
                .result-title { color: #333; border-bottom: 2px solid #007bff; }
                .json-data { background-color: #f8f9fa; padding: 10px; border-radius: 5px; }
                .success { color: green; }
                .method { font-weight: bold; color: #007bff; }
                pre { white-space: pre-wrap; word-wrap: break-word; }
                .summary { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>進階Substrate爬蟲報告</h1>
            <div class="summary">
                <h2>摘要</h2>
                <p>生成時間: 2025-06-16 18:10:52</p>
                <p>總請求數: 5</p>
                <p>JSON回應數: 0</p>
                <p>HTML回應數: 5</p>
            </div>
        <h2>未發現JSON資料</h2><p>所有請求都返回HTML頁面，這表示需要使用瀏覽器自動化工具。</p>
        </body>
        </html>
        