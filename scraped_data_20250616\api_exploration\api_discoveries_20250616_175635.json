[{"endpoint": "/api/substrate/overall", "url": "http://************:31539/api/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:56 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:51.033185", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/data", "url": "http://************:31539/api/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:57 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:51.577955", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/summary", "url": "http://************:31539/api/substrate/summary", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:57 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:52.157910", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/stations", "url": "http://************:31539/api/substrate/stations", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:52.783894", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/substrate/statistics", "url": "http://************:31539/api/substrate/statistics", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:53.371390", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/substrate", "url": "http://************:31539/api/data/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:56:59 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:53.957287", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/overall", "url": "http://************:31539/api/data/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:00 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:54.588014", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/data/stations", "url": "http://************:31539/api/data/stations", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:00 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:55.195983", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/dashboard/data", "url": "http://************:31539/api/dashboard/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:01 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:55.776808", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/dashboard/substrate", "url": "http://************:31539/api/dashboard/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:01 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:56.352421", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/statistics", "url": "http://************:31539/api/statistics", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:02 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:56.918956", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/reports", "url": "http://************:31539/api/reports", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:03 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:57.520335", "content_type": "text/html", "is_json": false}, {"endpoint": "/api/export", "url": "http://************:31539/api/export", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:03 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:58.094033", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/overall", "url": "http://************:31539/substrate/api/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:04 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:58.680579", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/data", "url": "http://************:31539/substrate/api/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:04 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:59.297430", "content_type": "text/html", "is_json": false}, {"endpoint": "/substrate/api/summary", "url": "http://************:31539/substrate/api/summary", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:05 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:55:59.901328", "content_type": "text/html", "is_json": false}, {"endpoint": "/data/api/substrate", "url": "http://************:31539/data/api/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:06 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:00.501482", "content_type": "text/html", "is_json": false}, {"endpoint": "/data/api/overall", "url": "http://************:31539/data/api/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:06 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:01.101431", "content_type": "text/html", "is_json": false}, {"endpoint": "/backend/api/substrate", "url": "http://************:31539/backend/api/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:07 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:01.702428", "content_type": "text/html", "is_json": false}, {"endpoint": "/backend/api/data", "url": "http://************:31539/backend/api/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:07 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:02.291508", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/substrate/overall", "url": "http://************:31539/v1/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:08 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:02.875913", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/substrate/data", "url": "http://************:31539/v1/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:09 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:03.455869", "content_type": "text/html", "is_json": false}, {"endpoint": "/v1/data/substrate", "url": "http://************:31539/v1/data/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:09 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:04.033029", "content_type": "text/html", "is_json": false}, {"endpoint": "/v2/substrate/overall", "url": "http://************:31539/v2/substrate/overall", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:10 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:04.623586", "content_type": "text/html", "is_json": false}, {"endpoint": "/v2/substrate/data", "url": "http://************:31539/v2/substrate/data", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 09:57:10 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T17:56:05.245756", "content_type": "text/html", "is_json": false}]