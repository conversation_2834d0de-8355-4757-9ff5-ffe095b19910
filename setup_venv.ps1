Write-Host "Checking Python installation..."

# Try different Python commands to find the right one
$pythonCommands = @("python", "python3", "py")
$pythonCommand = $null

foreach ($cmd in $pythonCommands) {
    try {
        $version = & $cmd --version 2>&1
        if ($version -match "Python") {
            $pythonCommand = $cmd
            Write-Host "Found Python: $version"
            break
        }
    } catch {
        # Command not found, continue to next
    }
}

if ($null -eq $pythonCommand) {
    Write-Host "Python not found. Please install Python and try again."
    exit 1
}

# Create virtual environment
Write-Host "Creating virtual environment..."
& $pythonCommand -m venv venv

# Check if venv was created
if (Test-Path -Path "venv") {
    Write-Host "Virtual environment created successfully!"
    Write-Host ""
    Write-Host "To activate the virtual environment:"
    Write-Host ".\venv\Scripts\Activate.ps1"
    Write-Host ""
    Write-Host "To install required packages (after activation):"
    Write-Host "pip install -r requirements.txt"
    Write-Host ""
    Write-Host "To deactivate the virtual environment:"
    Write-Host "deactivate"
} else {
    Write-Host "Failed to create virtual environment."
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
