#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版測試爬蟲 - 測試基本連接和JavaScript執行
"""

import pandas as pd
import json
import time
import os
from datetime import datetime

def test_basic_connection():
    """測試基本連接"""
    print("=" * 60)
    print("測試基本連接...")
    
    try:
        import urllib.request
        
        url = 'http://************:31539'
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        response = urllib.request.urlopen(req, timeout=10)
        content = response.read().decode('utf-8')
        
        print(f"✅ 基本連接成功")
        print(f"狀態碼: {response.getcode()}")
        print(f"內容長度: {len(content)} 字元")
        print(f"內容預覽: {content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本連接失敗: {str(e)}")
        return False

def test_requests_html():
    """測試 requests-html"""
    print("\n" + "=" * 60)
    print("測試 requests-html...")
    
    try:
        from requests_html import HTMLSession
        print("✅ requests-html 模組載入成功")
        
        session = HTMLSession()
        print("✅ HTMLSession 建立成功")
        
        url = 'http://************:31539'
        print(f"正在訪問: {url}")
        
        r = session.get(url, timeout=10)
        print(f"✅ GET請求成功，狀態碼: {r.status_code}")
        print(f"內容長度: {len(r.text)} 字元")
        
        # 測試JavaScript執行
        print("正在執行JavaScript...")
        r.html.render(wait=2, timeout=15)
        print("✅ JavaScript執行完成")
        
        print(f"渲染後內容長度: {len(r.html.text)} 字元")
        print(f"內容預覽: {r.html.text[:300]}...")
        
        return True, r
        
    except ImportError:
        print("❌ requests-html 未安裝")
        print("請執行: pip install requests-html")
        return False, None
    except Exception as e:
        print(f"❌ requests-html 測試失敗: {str(e)}")
        return False, None

def test_substrate_page():
    """測試 substrate 頁面"""
    print("\n" + "=" * 60)
    print("測試 substrate 頁面...")
    
    try:
        from requests_html import HTMLSession
        
        session = HTMLSession()
        
        # 先嘗試登入
        print("嘗試登入...")
        login_data = {
            'username': 'admin',
            'password': 'pega#1234'
        }
        
        login_response = session.post('http://************:31539/login', data=login_data, timeout=10)
        print(f"登入回應狀態碼: {login_response.status_code}")
        
        # 訪問 substrate 頁面
        substrate_url = 'http://************:31539/substrate/overall'
        print(f"正在訪問: {substrate_url}")
        
        r = session.get(substrate_url, timeout=10)
        print(f"✅ Substrate頁面訪問成功，狀態碼: {r.status_code}")
        
        # 執行JavaScript
        print("正在執行JavaScript...")
        r.html.render(wait=3, timeout=20)
        print("✅ JavaScript執行完成")
        
        # 分析內容
        text_content = r.html.text
        print(f"頁面文字內容長度: {len(text_content)} 字元")
        
        # 尋找關鍵字
        keywords = ['OK Image Count', 'NG Image Count', 'Uncertain Image Count', 'DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        found_keywords = []
        
        for keyword in keywords:
            if keyword in text_content:
                found_keywords.append(keyword)
        
        print(f"找到的關鍵字: {found_keywords}")
        
        # 儲存頁面內容供分析
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 建立輸出目錄
        output_dir = 'scraped_data_20250616/test_results'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 儲存HTML內容
        html_file = os.path.join(output_dir, f"substrate_page_{timestamp}.html")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(r.html.html)
        print(f"HTML內容已儲存: {html_file}")
        
        # 儲存文字內容
        text_file = os.path.join(output_dir, f"substrate_text_{timestamp}.txt")
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"文字內容已儲存: {text_file}")
        
        # 分析數字
        import re
        numbers = re.findall(r'\d+', text_content)
        large_numbers = [n for n in numbers if len(n) >= 3]
        
        print(f"找到的大數字 (>=3位): {large_numbers[:10]}...")  # 只顯示前10個
        
        return True, text_content
        
    except Exception as e:
        print(f"❌ Substrate頁面測試失敗: {str(e)}")
        return False, None

def load_and_test_input_data():
    """載入並測試輸入資料"""
    print("\n" + "=" * 60)
    print("測試輸入資料...")
    
    try:
        if os.path.exists('input_data.csv'):
            df = pd.read_csv('input_data.csv')
            print(f"✅ 成功載入 input_data.csv")
            print(f"資料筆數: {len(df)}")
            print("資料內容:")
            print(df.to_string())
            
            input_data = df.to_dict('records')
            print(f"\n轉換為字典格式: {input_data}")
            
            return True, input_data
        else:
            print("❌ 找不到 input_data.csv")
            return False, None
            
    except Exception as e:
        print(f"❌ 載入輸入資料失敗: {str(e)}")
        return False, None

def main():
    """主要測試函數"""
    print("Substrate Dashboard 爬蟲測試")
    print("測試時間:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 測試1: 基本連接
    basic_ok = test_basic_connection()
    
    # 測試2: requests-html
    requests_ok, response = test_requests_html()
    
    # 測試3: substrate頁面
    substrate_ok, content = test_substrate_page()
    
    # 測試4: 輸入資料
    input_ok, input_data = load_and_test_input_data()
    
    # 總結
    print("\n" + "=" * 60)
    print("測試總結:")
    print(f"基本連接: {'✅' if basic_ok else '❌'}")
    print(f"requests-html: {'✅' if requests_ok else '❌'}")
    print(f"substrate頁面: {'✅' if substrate_ok else '❌'}")
    print(f"輸入資料: {'✅' if input_ok else '❌'}")
    
    if all([basic_ok, requests_ok, substrate_ok, input_ok]):
        print("\n🎉 所有測試通過！可以執行完整爬蟲")
        print("建議執行: python requests_html_scraper.py")
    else:
        print("\n⚠️  部分測試失敗，請檢查上述錯誤訊息")
    
    print("\n📁 測試結果儲存在: scraped_data_20250616/test_results/")

if __name__ == "__main__":
    main()
