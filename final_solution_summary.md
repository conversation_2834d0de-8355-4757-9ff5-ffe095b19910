# Substrate Dashboard 爬蟲最終解決方案

## 🎯 任務目標

您需要一個爬蟲來：
1. **讀取 input_data.csv** 中的特定資料 (如 A06-192, 92540363)
2. **抓取三種圖片計數**：OK Image Count, NG Image Count, Uncertain Image Count  
3. **收集所有Station (DF1-DF6) 的詳細數據**：每個DF有9筆數據 (OK/NG/SKIP 在三種count下)

## ✅ 已完成的工作

我已經為您建立了完整的爬蟲系統：

### 📁 檔案清單
1. **`requests_html_scraper.py`** - 主要爬蟲 (推薦使用，無需WebDriver)
2. **`substrate_dashboard_scraper.py`** - Selenium版本 (功能最完整)
3. **`input_data.csv`** - 輸入資料檔案 (已修正格式)
4. **`substrate_scraper_guide.md`** - 詳細使用說明
5. **`simple_test_scraper.py`** - 測試工具
6. **`quick_test.py`** - 快速測試

### 📊 input_data.csv 格式 (已修正)
```csv
code,lot,ship
A06-192,92540363,ship001
a06198,92540397,ship002
```

## 🚀 立即執行步驟

### 方案1: 使用 requests-html (推薦)

```bash
# 1. 確認已安裝 requests-html (您已完成)
pip install requests-html pandas

# 2. 執行爬蟲
python requests_html_scraper.py
```

### 方案2: 使用 Selenium (如果方案1不行)

```bash
# 1. 安裝依賴
pip install selenium pandas webdriver-manager

# 2. 執行爬蟲
python substrate_dashboard_scraper.py
```

## 📋 預期輸出

### 輸出檔案位置
```
scraped_data_20250616/
├── requests_html_data/          # requests-html版本輸出
│   ├── requests_html_data_*.json
│   ├── requests_html_data_*.csv
│   └── report_*.html
├── dashboard_data/              # Selenium版本輸出
│   ├── dashboard_data_*.json
│   ├── dashboard_data_*.csv
│   └── summary_*.json
└── logs/                        # 執行日誌
```

### CSV輸出格式
| input_code | input_lot | input_ship | ok_image_count | ng_image_count | uncertain_image_count | station | station_ok | station_ng | station_skip |
|------------|-----------|------------|----------------|----------------|----------------------|---------|------------|------------|--------------|
| A06-192 | 92540363 | ship001 | 788 | 2,836 | 1,204 | DF1 | 數值1,數值2,數值3 | 數值1,數值2,數值3 | 數值1,數值2,數值3 |
| A06-192 | 92540363 | ship001 | 788 | 2,836 | 1,204 | DF2 | 數值1,數值2,數值3 | 數值1,數值2,數值3 | 數值1,數值2,數值3 |
| ... | ... | ... | ... | ... | ... | DF6 | ... | ... | ... |

### JSON輸出格式
```json
[
  {
    "input_data": {
      "code": "A06-192",
      "lot": "92540363", 
      "ship": "ship001"
    },
    "timestamp": "2025-06-16T18:30:00",
    "image_counts": {
      "OK_Image_Count": "788",
      "NG_Image_Count": "2,836",
      "Uncertain_Image_Count": "1,204"
    },
    "station_details": {
      "DF1": {
        "OK": ["數值1", "數值2", "數值3"],
        "NG": ["數值1", "數值2", "數值3"], 
        "SKIP": ["數值1", "數值2", "數值3"]
      },
      "DF2": { ... },
      "DF3": { ... },
      "DF4": { ... },
      "DF5": { ... },
      "DF6": { ... }
    }
  }
]
```

## 🔧 自訂設定

### 添加更多輸入資料
編輯 `input_data.csv`：
```csv
code,lot,ship
A06-192,92540363,ship001
a06198,92540397,ship002
b07299,93651408,ship003
c08300,94762519,ship004
```

### 修改Station列表
在爬蟲檔案中：
```python
self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
```

### 調整等待時間
```python
time.sleep(3)  # 增加或減少等待時間
r.html.render(wait=5)  # 增加JavaScript執行時間
```

## 🚨 故障排除

### 如果 requests-html 不工作
1. **檢查網路連接**：確認可以訪問 `http://************:31539`
2. **增加等待時間**：修改 `wait=3` 為 `wait=5`
3. **使用Selenium版本**：執行 `python substrate_dashboard_scraper.py`

### 如果找不到資料
1. **檢查網頁結構**：網站可能已更新
2. **查看日誌檔案**：檢查 `logs/` 目錄中的錯誤訊息
3. **手動測試**：在瀏覽器中確認網站正常運作

### 如果CSV格式錯誤
1. **檢查 input_data.csv**：確保有正確的標題行
2. **檢查編碼**：確保檔案是UTF-8編碼
3. **檢查分隔符**：確保使用逗號分隔

## 📈 進階功能

### 批次處理大量資料
```python
# 每處理5筆資料後休息10秒
if i % 5 == 0:
    time.sleep(10)
```

### 錯誤重試機制
```python
# 在爬蟲中添加重試邏輯
for retry in range(3):
    try:
        result = scrape_data()
        break
    except:
        if retry < 2:
            time.sleep(5)
            continue
        else:
            raise
```

### 資料驗證
```python
# 檢查抓取的資料是否完整
if len(station_details) != 6:
    print("警告：未抓取到所有Station的資料")
```

## 🎯 執行建議

### 第一次執行
1. **先測試單筆資料**：在 input_data.csv 中只放一筆資料
2. **檢查輸出格式**：確認資料格式符合需求
3. **調整參數**：根據網站回應速度調整等待時間

### 生產環境執行
1. **添加完整的輸入資料**
2. **設定適當的間隔時間**：避免對伺服器造成負擔
3. **監控執行狀況**：定期檢查日誌檔案

## 📞 技術支援

### 檢查清單
- [ ] input_data.csv 格式正確
- [ ] 網站 http://************:31539 可正常訪問
- [ ] requests-html 已安裝
- [ ] 有足夠的磁碟空間儲存結果

### 常見錯誤
1. **ModuleNotFoundError**: 執行 `pip install requests-html pandas`
2. **TimeoutError**: 增加 timeout 參數
3. **ConnectionError**: 檢查網路連接
4. **FileNotFoundError**: 確認 input_data.csv 存在

---

## 🎉 總結

您現在擁有一個完整的Substrate Dashboard爬蟲系統，能夠：

✅ **自動讀取輸入資料** (A06-192, 92540363等)  
✅ **抓取三種圖片計數** (OK/NG/Uncertain Image Count)  
✅ **收集所有Station資料** (DF1-DF6，每個9筆數據)  
✅ **輸出多種格式** (JSON, CSV, HTML報告)  
✅ **完整的錯誤處理和日誌記錄**  

**立即開始**: 執行 `python requests_html_scraper.py`
