@echo off
echo ========================================
echo 激活虚拟环境
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "venv" (
    echo 虚拟环境不存在，请先运行 create_venv.bat 创建虚拟环境
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 显示当前Python路径
echo.
echo 当前Python路径:
where python
echo.

REM 显示已安装的包
echo 已安装的包:
pip list
echo.

echo ========================================
echo 虚拟环境已激活！
echo 现在您可以运行以下命令:
echo.
echo 测试连接:     python test_connection.py
echo 运行爬虫:     python run_scraper.py
echo 高级爬虫:     python advanced_scraper.py
echo.
echo 要退出虚拟环境，请输入: deactivate
echo ========================================

REM 保持命令行窗口打开
cmd /k
