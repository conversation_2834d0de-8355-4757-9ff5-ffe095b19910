#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版爬蟲 - 使用Python內建模組
抓取 ************:31539 的資料
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import csv
import time
import os
from datetime import datetime
import html.parser
import re

class SimpleHTMLParser(html.parser.HTMLParser):
    """簡單的HTML解析器"""
    def __init__(self):
        super().__init__()
        self.data = []
        self.links = []
        self.tables = []
        self.current_table = None
        self.current_row = None
        self.current_cell = ""
        self.in_table = False
        self.in_row = False
        self.in_cell = False
        
    def handle_starttag(self, tag, attrs):
        if tag == 'a':
            href = dict(attrs).get('href', '')
            if href:
                self.links.append(href)
        elif tag == 'table':
            self.in_table = True
            self.current_table = []
        elif tag == 'tr' and self.in_table:
            self.in_row = True
            self.current_row = []
        elif tag in ['td', 'th'] and self.in_row:
            self.in_cell = True
            self.current_cell = ""
            
    def handle_endtag(self, tag):
        if tag == 'table':
            self.in_table = False
            if self.current_table:
                self.tables.append(self.current_table)
            self.current_table = None
        elif tag == 'tr' and self.in_table:
            self.in_row = False
            if self.current_row:
                self.current_table.append(self.current_row)
            self.current_row = None
        elif tag in ['td', 'th'] and self.in_cell:
            self.in_cell = False
            self.current_row.append(self.current_cell.strip())
            self.current_cell = ""
            
    def handle_data(self, data):
        if self.in_cell:
            self.current_cell += data
        else:
            self.data.append(data.strip())

class SimpleScraper:
    def __init__(self):
        """初始化爬蟲"""
        self.base_url = 'http://************:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        self.session_cookies = {}
        self.scraped_data = []
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.data_dir = os.path.join(self.output_dir, 'data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.output_dir, self.data_dir, self.log_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def make_request(self, url, data=None, method='GET'):
        """發送HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                data = urllib.parse.urlencode(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/x-www-form-urlencoded')
            else:
                req = urllib.request.Request(url)
            
            # 添加標頭
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            return response.read().decode('utf-8'), response.getcode()
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None
    
    def login(self):
        """登入系統"""
        self.log("開始登入...")
        
        # 首先訪問登入頁面
        login_url = f"{self.base_url}/login"
        content, status = self.make_request(login_url)
        
        if status == 200:
            self.log("成功訪問登入頁面")
            
            # 準備登入資料
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 發送登入請求
            content, status = self.make_request(login_url, login_data, 'POST')
            
            if status == 200:
                self.log("登入請求已發送")
                # 檢查是否登入成功（可以根據回應內容調整）
                if 'dashboard' in content.lower() or 'main' in content.lower() or len(self.session_cookies) > 0:
                    self.log("登入成功！")
                    return True
                else:
                    self.log("登入可能失敗，但繼續嘗試抓取資料...")
                    return True  # 即使登入狀態不確定，也嘗試繼續
            else:
                self.log(f"登入失敗，狀態碼: {status}")
                return False
        else:
            self.log(f"無法訪問登入頁面，狀態碼: {status}")
            return False
    
    def scrape_page(self, url):
        """抓取單個頁面"""
        self.log(f"開始抓取頁面: {url}")
        
        content, status = self.make_request(url)
        
        if status == 200 and content:
            # 解析HTML
            parser = SimpleHTMLParser()
            parser.feed(content)
            
            # 提取頁面資料
            page_data = {
                'url': url,
                'timestamp': datetime.now().isoformat(),
                'status_code': status,
                'content_length': len(content),
                'text_data': [data for data in parser.data if data],
                'links': parser.links,
                'tables': parser.tables,
                'raw_html': content
            }
            
            self.scraped_data.append(page_data)
            self.log(f"成功抓取頁面: {url}")
            return page_data
        else:
            self.log(f"抓取頁面失敗: {url}, 狀態碼: {status}")
            return None
    
    def scrape_all_pages(self):
        """抓取所有頁面"""
        urls_to_scrape = [
            f"{self.base_url}",
            f"{self.base_url}/substrate/overall",
            f"{self.base_url}/data",
            f"{self.base_url}/dashboard",
            f"{self.base_url}/status"
        ]
        
        self.log(f"開始抓取 {len(urls_to_scrape)} 個頁面...")
        
        for url in urls_to_scrape:
            self.scrape_page(url)
            time.sleep(1)  # 延遲1秒
        
        self.log(f"完成抓取，共抓取 {len(self.scraped_data)} 個頁面")
    
    def save_data(self):
        """儲存資料"""
        if not self.scraped_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存JSON格式
        json_file = os.path.join(self.data_dir, f"scraped_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        self.log(f"資料已儲存為JSON格式: {json_file}")
        
        # 儲存CSV格式
        csv_file = os.path.join(self.data_dir, f"scraped_data_{timestamp}.csv")
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if self.scraped_data:
                fieldnames = ['url', 'timestamp', 'status_code', 'content_length', 'links_count', 'tables_count']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for page in self.scraped_data:
                    row = {
                        'url': page['url'],
                        'timestamp': page['timestamp'],
                        'status_code': page['status_code'],
                        'content_length': page['content_length'],
                        'links_count': len(page['links']),
                        'tables_count': len(page['tables'])
                    }
                    writer.writerow(row)
        self.log(f"資料已儲存為CSV格式: {csv_file}")
        
        # 儲存HTML報告
        html_file = os.path.join(self.data_dir, f"report_{timestamp}.html")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_html_report())
        self.log(f"報告已儲存為HTML格式: {html_file}")
    
    def generate_html_report(self):
        """生成HTML報告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>爬蟲資料報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .page {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .page-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .timestamp {{ color: #666; font-size: 0.9em; }}
            </style>
        </head>
        <body>
            <h1>爬蟲資料報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>共抓取 {len(self.scraped_data)} 個頁面</p>
        """
        
        for page in self.scraped_data:
            html += f"""
            <div class="page">
                <h2 class="page-title">頁面: {page['url']}</h2>
                <p class="timestamp">抓取時間: {page['timestamp']}</p>
                <p>狀態碼: {page['status_code']}</p>
                <p>內容長度: {page['content_length']} 字元</p>
                <p>連結數量: {len(page['links'])}</p>
                <p>表格數量: {len(page['tables'])}</p>
                
                <h3>連結</h3>
                <ul>
            """
            
            for link in page['links'][:10]:  # 只顯示前10個連結
                html += f"<li>{link}</li>"
            
            html += "</ul>"
            
            if page['tables']:
                html += "<h3>表格資料</h3>"
                for i, table in enumerate(page['tables'][:3]):  # 只顯示前3個表格
                    html += f"<h4>表格 {i+1}</h4><table>"
                    for row in table[:10]:  # 只顯示前10行
                        html += "<tr>"
                        for cell in row:
                            html += f"<td>{cell}</td>"
                        html += "</tr>"
                    html += "</table>"
            
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        return html
    
    def run(self):
        """執行爬蟲"""
        self.log("開始執行爬蟲...")
        
        try:
            # 登入
            if self.login():
                # 抓取資料
                self.scrape_all_pages()
                
                # 儲存資料
                self.save_data()
                
                self.log("爬蟲執行完成！")
                return True
            else:
                self.log("登入失敗，但嘗試繼續抓取...")
                self.scrape_all_pages()
                self.save_data()
                return True
                
        except Exception as e:
            self.log(f"爬蟲執行過程中發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 50)
    print("簡化版爬蟲 - ************:31539")
    print("=" * 50)
    
    scraper = SimpleScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ 爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.data_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ 爬蟲執行失敗")
