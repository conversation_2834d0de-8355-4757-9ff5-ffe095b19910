#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级爬虫 - 使用Selenium处理JavaScript渲染的页面
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json
import time
import logging
from datetime import datetime
from config import SERVER_CONFIG, AUTH_CONFIG, REQUEST_CONFIG, OUTPUT_CONFIG, create_directories

class AdvancedWebScraper:
    def __init__(self, headless=True):
        """初始化高级爬虫"""
        self.driver = None
        self.headless = headless
        self.scraped_data = []
        
        # 创建必要的目录
        create_directories()
        
        # 设置日志
        self.setup_logging()
        
        # 初始化浏览器
        self.setup_driver()
        
    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"{OUTPUT_CONFIG['log_dir']}/advanced_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={REQUEST_CONFIG["headers"]["User-Agent"]}')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            self.logger.info("Chrome浏览器驱动初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化浏览器驱动失败: {str(e)}")
            self.logger.info("请确保已安装Chrome浏览器和ChromeDriver")
            raise
    
    def login(self):
        """使用Selenium登录"""
        try:
            self.logger.info("开始登录...")
            
            # 访问登录页面
            self.driver.get(SERVER_CONFIG['login_url'])
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 查找用户名和密码输入框
            username_selectors = [
                'input[name="username"]',
                'input[name="user"]',
                'input[name="login"]',
                'input[type="text"]',
                '#username',
                '#user',
                '#login'
            ]
            
            password_selectors = [
                'input[name="password"]',
                'input[name="pass"]',
                'input[type="password"]',
                '#password',
                '#pass'
            ]
            
            username_field = None
            password_field = None
            
            # 尝试找到用户名输入框
            for selector in username_selectors:
                try:
                    username_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            # 尝试找到密码输入框
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if username_field and password_field:
                # 输入用户名和密码
                username_field.clear()
                username_field.send_keys(AUTH_CONFIG['username'])
                
                password_field.clear()
                password_field.send_keys(AUTH_CONFIG['password'])
                
                # 查找并点击登录按钮
                login_button_selectors = [
                    'input[type="submit"]',
                    'button[type="submit"]',
                    'button:contains("登录")',
                    'button:contains("Login")',
                    'input[value*="登录"]',
                    'input[value*="Login"]'
                ]
                
                login_button = None
                for selector in login_button_selectors:
                    try:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        break
                    except NoSuchElementException:
                        continue
                
                if login_button:
                    login_button.click()
                    
                    # 等待登录完成
                    time.sleep(3)
                    
                    # 检查是否登录成功
                    current_url = self.driver.current_url
                    if current_url != SERVER_CONFIG['login_url']:
                        self.logger.info("登录成功！")
                        return True
                    else:
                        self.logger.warning("登录可能失败，仍在登录页面")
                        return False
                else:
                    self.logger.error("找不到登录按钮")
                    return False
            else:
                self.logger.error("找不到用户名或密码输入框")
                return False
                
        except Exception as e:
            self.logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    def scrape_page(self, url):
        """使用Selenium抓取页面"""
        try:
            self.logger.info(f"开始抓取页面: {url}")
            
            self.driver.get(url)
            
            # 等待页面加载完成
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待JavaScript执行
            time.sleep(2)
            
            # 获取页面数据
            page_data = {
                'url': url,
                'timestamp': datetime.now().isoformat(),
                'title': self.driver.title,
                'current_url': self.driver.current_url,
                'page_source': self.driver.page_source,
                'tables': self.extract_tables_selenium(),
                'forms': self.extract_forms_selenium(),
                'text_content': self.driver.find_element(By.TAG_NAME, "body").text
            }
            
            self.scraped_data.append(page_data)
            self.logger.info(f"成功抓取页面: {url}")
            return page_data
            
        except Exception as e:
            self.logger.error(f"抓取页面时发生错误: {str(e)}, URL: {url}")
            return None
    
    def extract_tables_selenium(self):
        """使用Selenium提取表格数据"""
        tables = []
        
        try:
            table_elements = self.driver.find_elements(By.TAG_NAME, "table")
            
            for table in table_elements:
                table_data = {
                    'headers': [],
                    'rows': []
                }
                
                # 提取表头
                headers = table.find_elements(By.TAG_NAME, "th")
                if headers:
                    table_data['headers'] = [th.text.strip() for th in headers]
                
                # 提取表格行
                rows = table.find_elements(By.TAG_NAME, "tr")
                for row in rows:
                    cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                    if cells:
                        row_data = [cell.text.strip() for cell in cells]
                        table_data['rows'].append(row_data)
                
                if table_data['rows']:
                    tables.append(table_data)
                    
        except Exception as e:
            self.logger.warning(f"提取表格数据时发生错误: {str(e)}")
        
        return tables
    
    def extract_forms_selenium(self):
        """使用Selenium提取表单信息"""
        forms = []
        
        try:
            form_elements = self.driver.find_elements(By.TAG_NAME, "form")
            
            for form in form_elements:
                form_data = {
                    'action': form.get_attribute('action') or '',
                    'method': form.get_attribute('method') or 'GET',
                    'fields': []
                }
                
                # 提取表单字段
                inputs = form.find_elements(By.CSS_SELECTOR, "input, select, textarea")
                for input_field in inputs:
                    field_info = {
                        'name': input_field.get_attribute('name') or '',
                        'type': input_field.get_attribute('type') or '',
                        'value': input_field.get_attribute('value') or ''
                    }
                    form_data['fields'].append(field_info)
                
                forms.append(form_data)
                
        except Exception as e:
            self.logger.warning(f"提取表单信息时发生错误: {str(e)}")
        
        return forms

    def scrape_all_pages(self):
        """抓取所有配置的页面"""
        self.logger.info("开始抓取所有页面...")

        for url in SERVER_CONFIG['data_urls']:
            self.scrape_page(url)
            time.sleep(REQUEST_CONFIG['delay_between_requests'])

        self.logger.info(f"完成抓取，共抓取 {len(self.scraped_data)} 个页面")
        return True

    def save_data(self, format_type='json'):
        """保存抓取的数据"""
        if not self.scraped_data:
            self.logger.warning("没有数据可保存")
            return

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format_type == 'json':
            filename = f"{OUTPUT_CONFIG['data_dir']}/advanced_scraped_data_{timestamp}.json"
            # 为了节省空间，不保存完整的page_source
            simplified_data = []
            for page in self.scraped_data:
                simplified_page = page.copy()
                simplified_page['page_source'] = simplified_page['page_source'][:5000] + "..." if len(simplified_page['page_source']) > 5000 else simplified_page['page_source']
                simplified_data.append(simplified_page)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(simplified_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已保存为JSON格式: {filename}")

    def run(self):
        """运行高级爬虫的主方法"""
        self.logger.info("开始运行高级爬虫...")

        try:
            # 登录
            if self.login():
                # 抓取数据
                if self.scrape_all_pages():
                    # 保存数据
                    self.save_data('json')
                    self.logger.info("高级爬虫运行完成！")
                    return True
                else:
                    self.logger.error("数据抓取失败")
                    return False
            else:
                self.logger.error("登录失败，爬虫无法运行")
                return False

        except Exception as e:
            self.logger.error(f"爬虫运行过程中发生错误: {str(e)}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    scraper = AdvancedWebScraper(headless=False)  # 设置为False可以看到浏览器操作
    scraper.run()
