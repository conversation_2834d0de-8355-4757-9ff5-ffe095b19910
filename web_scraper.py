#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Scraper for 10.22.22.144:31539
抓取指定服务器的数据
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
import logging
from datetime import datetime
import os
from config import SERVER_CONFIG, AUTH_CONFIG, REQUEST_CONFIG, OUTPUT_CONFIG, create_directories

class WebScraper:
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.session.headers.update(REQUEST_CONFIG['headers'])
        self.logged_in = False
        self.scraped_data = []
        
        # 创建必要的目录
        create_directories()
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"{OUTPUT_CONFIG['log_dir']}/scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def login(self):
        """登录到服务器"""
        try:
            self.logger.info("开始登录...")
            
            # 首先访问登录页面获取可能的CSRF token或其他必要信息
            login_page = self.session.get(SERVER_CONFIG['login_url'], timeout=REQUEST_CONFIG['timeout'])
            
            if login_page.status_code == 200:
                self.logger.info("成功访问登录页面")
                
                # 准备登录数据
                login_data = {
                    'username': AUTH_CONFIG['username'],
                    'password': AUTH_CONFIG['password']
                }
                
                # 尝试POST登录
                response = self.session.post(
                    SERVER_CONFIG['login_url'], 
                    data=login_data,
                    timeout=REQUEST_CONFIG['timeout']
                )
                
                if response.status_code == 200:
                    # 检查是否登录成功（可以根据响应内容调整判断逻辑）
                    if 'dashboard' in response.url.lower() or 'main' in response.url.lower() or len(response.cookies) > 0:
                        self.logged_in = True
                        self.logger.info("登录成功！")
                        return True
                    else:
                        self.logger.warning("登录可能失败，检查响应内容...")
                        # 可以在这里添加更详细的登录状态检查
                        
                self.logger.error(f"登录失败，状态码: {response.status_code}")
                return False
                
            else:
                self.logger.error(f"无法访问登录页面，状态码: {login_page.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    def scrape_page(self, url):
        """抓取单个页面的数据"""
        try:
            self.logger.info(f"开始抓取页面: {url}")
            
            response = self.session.get(url, timeout=REQUEST_CONFIG['timeout'])
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取页面数据
                page_data = {
                    'url': url,
                    'timestamp': datetime.now().isoformat(),
                    'title': soup.title.string if soup.title else '',
                    'content': self.extract_content(soup),
                    'tables': self.extract_tables(soup),
                    'forms': self.extract_forms(soup),
                    'raw_html': response.text
                }
                
                self.scraped_data.append(page_data)
                self.logger.info(f"成功抓取页面: {url}")
                return page_data
                
            else:
                self.logger.error(f"抓取页面失败，状态码: {response.status_code}, URL: {url}")
                return None
                
        except Exception as e:
            self.logger.error(f"抓取页面时发生错误: {str(e)}, URL: {url}")
            return None
    
    def extract_content(self, soup):
        """提取页面主要内容"""
        content = {}
        
        # 提取所有文本内容
        content['text'] = soup.get_text(strip=True)
        
        # 提取所有链接
        links = []
        for link in soup.find_all('a', href=True):
            links.append({
                'text': link.get_text(strip=True),
                'href': link['href']
            })
        content['links'] = links
        
        # 提取所有图片
        images = []
        for img in soup.find_all('img', src=True):
            images.append({
                'alt': img.get('alt', ''),
                'src': img['src']
            })
        content['images'] = images
        
        return content
    
    def extract_tables(self, soup):
        """提取页面中的表格数据"""
        tables = []
        
        for table in soup.find_all('table'):
            table_data = {
                'headers': [],
                'rows': []
            }
            
            # 提取表头
            headers = table.find_all('th')
            if headers:
                table_data['headers'] = [th.get_text(strip=True) for th in headers]
            
            # 提取表格行
            for row in table.find_all('tr'):
                cells = row.find_all(['td', 'th'])
                if cells:
                    row_data = [cell.get_text(strip=True) for cell in cells]
                    table_data['rows'].append(row_data)
            
            if table_data['rows']:  # 只添加非空表格
                tables.append(table_data)
        
        return tables
    
    def extract_forms(self, soup):
        """提取页面中的表单信息"""
        forms = []
        
        for form in soup.find_all('form'):
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'fields': []
            }
            
            # 提取表单字段
            for input_field in form.find_all(['input', 'select', 'textarea']):
                field_info = {
                    'name': input_field.get('name', ''),
                    'type': input_field.get('type', ''),
                    'value': input_field.get('value', '')
                }
                form_data['fields'].append(field_info)
            
            forms.append(form_data)
        
        return forms

    def scrape_all_pages(self):
        """抓取所有配置的页面"""
        if not self.logged_in:
            if not self.login():
                self.logger.error("登录失败，无法继续抓取")
                return False

        self.logger.info("开始抓取所有页面...")

        for url in SERVER_CONFIG['data_urls']:
            self.scrape_page(url)
            # 在请求之间添加延迟
            time.sleep(REQUEST_CONFIG['delay_between_requests'])

        self.logger.info(f"完成抓取，共抓取 {len(self.scraped_data)} 个页面")
        return True

    def save_data(self, format_type='json'):
        """保存抓取的数据"""
        if not self.scraped_data:
            self.logger.warning("没有数据可保存")
            return

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format_type == 'json':
            filename = f"{OUTPUT_CONFIG['data_dir']}/scraped_data_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已保存为JSON格式: {filename}")

        elif format_type == 'csv':
            filename = f"{OUTPUT_CONFIG['data_dir']}/scraped_data_{timestamp}.csv"
            # 将数据扁平化以适合CSV格式
            flattened_data = []
            for page in self.scraped_data:
                flat_page = {
                    'url': page['url'],
                    'timestamp': page['timestamp'],
                    'title': page['title'],
                    'text_content': page['content']['text'][:1000],  # 限制长度
                    'links_count': len(page['content']['links']),
                    'tables_count': len(page['tables']),
                    'forms_count': len(page['forms'])
                }
                flattened_data.append(flat_page)

            with open(filename, 'w', newline='', encoding='utf-8') as f:
                if flattened_data:
                    writer = csv.DictWriter(f, fieldnames=flattened_data[0].keys())
                    writer.writeheader()
                    writer.writerows(flattened_data)
            self.logger.info(f"数据已保存为CSV格式: {filename}")

        elif format_type == 'html':
            filename = f"{OUTPUT_CONFIG['data_dir']}/scraped_data_{timestamp}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.generate_html_report())
            self.logger.info(f"数据已保存为HTML格式: {filename}")

    def generate_html_report(self):
        """生成HTML格式的报告"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>爬虫数据报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .page { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
                .page-title { color: #333; border-bottom: 2px solid #007bff; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .timestamp { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>爬虫数据报告</h1>
            <p>生成时间: {}</p>
        """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        for page in self.scraped_data:
            html += f"""
            <div class="page">
                <h2 class="page-title">{page['title'] or 'No Title'}</h2>
                <p class="timestamp">URL: {page['url']}</p>
                <p class="timestamp">抓取时间: {page['timestamp']}</p>

                <h3>页面内容摘要</h3>
                <p>{page['content']['text'][:500]}...</p>

                <h3>链接 ({len(page['content']['links'])} 个)</h3>
                <ul>
            """

            for link in page['content']['links'][:10]:  # 只显示前10个链接
                html += f"<li><a href='{link['href']}'>{link['text']}</a></li>"

            html += "</ul>"

            # 添加表格数据
            if page['tables']:
                html += f"<h3>表格数据 ({len(page['tables'])} 个表格)</h3>"
                for i, table in enumerate(page['tables']):
                    html += f"<h4>表格 {i+1}</h4><table>"
                    if table['headers']:
                        html += "<tr>"
                        for header in table['headers']:
                            html += f"<th>{header}</th>"
                        html += "</tr>"

                    for row in table['rows'][:20]:  # 只显示前20行
                        html += "<tr>"
                        for cell in row:
                            html += f"<td>{cell}</td>"
                        html += "</tr>"
                    html += "</table>"

            html += "</div>"

        html += """
        </body>
        </html>
        """
        return html

    def run(self):
        """运行爬虫的主方法"""
        self.logger.info("开始运行爬虫...")

        try:
            # 登录
            if self.login():
                # 抓取数据
                if self.scrape_all_pages():
                    # 保存数据（多种格式）
                    for format_type in OUTPUT_CONFIG['formats']:
                        self.save_data(format_type)

                    self.logger.info("爬虫运行完成！")
                    return True
                else:
                    self.logger.error("数据抓取失败")
                    return False
            else:
                self.logger.error("登录失败，爬虫无法运行")
                return False

        except Exception as e:
            self.logger.error(f"爬虫运行过程中发生错误: {str(e)}")
            return False
        finally:
            self.session.close()

if __name__ == "__main__":
    scraper = WebScraper()
    scraper.run()
