print("ddd")

import requests
from bs4 import BeautifulSoup

import requests

# 登入網站的URL
url = "http://************:31539/login"

# 登入表單的資料
data = {
    "username": "your_username",
    "password": "999"
}

# 發送POST請求
response = requests.post(url, data=data)

# 如果登入成功，會得到一個Cookie
if response.status_code == 200:
    cookie = response.cookies
    print("登入成功！")
else:
    print("登入失敗！")

import requests

# 登入網站的URL
url = "http://************:31539/login"

# 登入JSON資料
data = {
    "username": "your_username",
    "password": "your_password"
}

# 發送POST請求
response = requests.post(url, json=data)

# 如果登入成功，會得到一個Cookie
if response.status_code == 200:
    cookie = response.cookies
    print("登入成功！")
else:
    print("登入失敗！")

import requests
from bs4 import BeautifulSoup

# 登入網站
url = "http://************:31539/login"
data = {"username": "admin", "password": "pega#1234"}
response = requests.post(url, data=data)

# 取得Cookie
cookie = response.cookies
response
# # 發送資料請求
# url = "http://************:31539/data"
# response = requests.get(url, cookies=cookie)

# # 解析HTML
# soup = BeautifulSoup(response.content, "html.parser")

# # 取得資料
# data = soup.find("table", {"id": "data-table"}).text
# print(data)

import requests
from bs4 import BeautifulSoup

url = "http://************:31539/substrate/overall"
response = requests.get(url)

if response.status_code == 200:
    soup = BeautifulSoup(response.text, 'html.parser')
    print(soup.prettify())
else:
    print("Failed to retrieve the webpage")



#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 requests-html 的 Substrate Dashboard 爬蟲
無需Chrome WebDriver，自動處理JavaScript
"""

import pandas as pd
import json
import time
import os
from datetime import datetime
from requests_html import HTMLSession
import re

class RequestsHTMLScraper:
    def __init__(self):
        """初始化requests-html爬蟲"""
        self.base_url = 'http://************:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.requests_dir = os.path.join(self.output_dir, 'requests_html_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.requests_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"requests_html_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # 初始化session
        self.session = HTMLSession()
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
        # 儲存所有抓取的資料
        self.all_data = []
        self.input_data = []
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def load_input_data(self, csv_file='input_data.csv'):
        """載入輸入資料"""
        try:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                self.input_data = df.to_dict('records')
                self.log(f"成功載入 {len(self.input_data)} 筆輸入資料")
                return True
            else:
                self.log(f"找不到檔案: {csv_file}")
                return False
        except Exception as e:
            self.log(f"載入輸入資料失敗: {str(e)}")
            return False
    
    def login(self):
        """登入系統"""
        try:
            self.log("開始登入...")
            
            # 訪問主頁面
            r = self.session.get(self.base_url)
            
            if r.status_code == 200:
                self.log("成功訪問主頁面")
                
                # 嘗試登入
                login_data = {
                    'username': self.username,
                    'password': self.password
                }
                
                login_response = self.session.post(f"{self.base_url}/login", data=login_data)
                
                if login_response.status_code == 200:
                    self.log("登入請求成功")
                    return True
                else:
                    self.log(f"登入失敗，狀態碼: {login_response.status_code}")
                    return False
            else:
                self.log(f"無法訪問主頁面，狀態碼: {r.status_code}")
                return False
                
        except Exception as e:
            self.log(f"登入失敗: {str(e)}")
            return False
    
    def scrape_substrate_page(self, input_row):
        """抓取substrate頁面資料"""
        try:
            self.log(f"抓取substrate頁面資料: {input_row}")
            
            # 訪問substrate頁面
            url = f"{self.base_url}/substrate/overall"
            r = self.session.get(url)
            
            if r.status_code != 200:
                self.log(f"無法訪問substrate頁面，狀態碼: {r.status_code}")
                return None
            
            # 執行JavaScript
            self.log("執行JavaScript...")
            r.html.render(wait=3, timeout=20)
            
            # 分析頁面內容
            result = {
                'input_data': input_row,
                'timestamp': datetime.now().isoformat(),
                'image_counts': {},
                'station_details': {},
                'raw_html': r.html.html[:1000] + "..." if len(r.html.html) > 1000 else r.html.html
            }
            
            # 抓取圖片計數
            self.extract_image_counts_from_html(r.html, result)
            
            # 抓取Station詳細資料
            self.extract_station_details_from_html(r.html, result)
            
            return result
            
        except Exception as e:
            self.log(f"抓取substrate頁面失敗: {str(e)}")
            return None
    
    def extract_image_counts_from_html(self, html, result):
        """從HTML中抓取圖片計數"""
        try:
            # 尋找包含數字的元素
            text_content = html.text
            
            # 使用正則表達式尋找數字模式
            numbers = re.findall(r'\d+', text_content)
            
            # 尋找特定的計數標籤
            if 'OK Image Count' in text_content:
                ok_match = re.search(r'OK Image Count[:\s]*(\d+)', text_content)
                if ok_match:
                    result['image_counts']['OK_Image_Count'] = ok_match.group(1)
            
            if 'NG Image Count' in text_content:
                ng_match = re.search(r'NG Image Count[:\s]*(\d+)', text_content)
                if ng_match:
                    result['image_counts']['NG_Image_Count'] = ng_match.group(1)
            
            if 'Uncertain Image Count' in text_content:
                uncertain_match = re.search(r'Uncertain Image Count[:\s]*(\d+)', text_content)
                if uncertain_match:
                    result['image_counts']['Uncertain_Image_Count'] = uncertain_match.group(1)
            
            # 如果沒有找到特定標籤，嘗試從頁面結構中推斷
            if not result['image_counts']:
                # 尋找所有數字並嘗試分類
                large_numbers = [n for n in numbers if len(n) >= 3]
                if len(large_numbers) >= 3:
                    result['image_counts'] = {
                        'OK_Image_Count': large_numbers[0] if len(large_numbers) > 0 else '0',
                        'NG_Image_Count': large_numbers[1] if len(large_numbers) > 1 else '0',
                        'Uncertain_Image_Count': large_numbers[2] if len(large_numbers) > 2 else '0'
                    }
            
            self.log(f"抓取到圖片計數: {result['image_counts']}")
            
        except Exception as e:
            self.log(f"抓取圖片計數失敗: {str(e)}")
    
    def extract_station_details_from_html(self, html, result):
        """從HTML中抓取Station詳細資料"""
        try:
            text_content = html.text
            
            for station in self.stations:
                result['station_details'][station] = {
                    'OK': [],
                    'NG': [],
                    'SKIP': []
                }
                
                # 尋找包含Station名稱的區域
                if station in text_content:
                    # 使用正則表達式尋找該Station附近的數字
                    station_pattern = rf'{station}[^\d]*(\d+)[^\d]*(\d+)[^\d]*(\d+)'
                    station_match = re.search(station_pattern, text_content)
                    
                    if station_match:
                        result['station_details'][station] = {
                            'OK': [station_match.group(1)],
                            'NG': [station_match.group(2)],
                            'SKIP': [station_match.group(3)]
                        }
                        self.log(f"找到 {station} 的數據: OK={station_match.group(1)}, NG={station_match.group(2)}, SKIP={station_match.group(3)}")
            
        except Exception as e:
            self.log(f"抓取Station詳細資料失敗: {str(e)}")
    
    def save_data(self):
        """儲存所有資料"""
        if not self.all_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整JSON資料
        json_file = os.path.join(self.requests_dir, f"requests_html_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.all_data, f, ensure_ascii=False, indent=2)
        self.log(f"完整資料已儲存: {json_file}")
        
        # 轉換為CSV格式
        csv_data = []
        for record in self.all_data:
            base_row = {
                'input_code': record['input_data'].get('code', ''),
                'input_lot': record['input_data'].get('lot', ''),
                'input_ship': record['input_data'].get('ship', ''),
                'timestamp': record['timestamp'],
                'ok_image_count': record['image_counts'].get('OK_Image_Count', ''),
                'ng_image_count': record['image_counts'].get('NG_Image_Count', ''),
                'uncertain_image_count': record['image_counts'].get('Uncertain_Image_Count', '')
            }
            
            # 添加每個Station的詳細數據
            for station, details in record['station_details'].items():
                station_row = base_row.copy()
                station_row['station'] = station
                station_row['station_ok'] = ','.join(details.get('OK', []))
                station_row['station_ng'] = ','.join(details.get('NG', []))
                station_row['station_skip'] = ','.join(details.get('SKIP', []))
                csv_data.append(station_row)
        
        # 儲存CSV檔案
        csv_file = os.path.join(self.requests_dir, f"requests_html_data_{timestamp}.csv")
        if csv_data:
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False)
            self.log(f"CSV資料已儲存: {csv_file}")
        
        # 生成HTML報告
        self.generate_html_report(timestamp)
    
    def generate_html_report(self, timestamp):
        """生成HTML報告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Requests-HTML 爬蟲報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .record {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .record-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                .data-section {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                .station-data {{ display: inline-block; margin: 5px; padding: 5px; border: 1px solid #ddd; }}
                pre {{ white-space: pre-wrap; word-wrap: break-word; }}
            </style>
        </head>
        <body>
            <h1>Requests-HTML 爬蟲報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>處理記錄數: {len(self.all_data)}</p>
        """
        
        for i, record in enumerate(self.all_data):
            html_content += f"""
            <div class="record">
                <h2 class="record-title">記錄 {i+1}</h2>
                <h3>輸入資料</h3>
                <div class="data-section">
                    <p><strong>Code:</strong> {record['input_data'].get('code', 'N/A')}</p>
                    <p><strong>Lot:</strong> {record['input_data'].get('lot', 'N/A')}</p>
                    <p><strong>Ship:</strong> {record['input_data'].get('ship', 'N/A')}</p>
                </div>
                
                <h3>圖片計數</h3>
                <div class="data-section">
                    <p><strong>OK Image Count:</strong> {record['image_counts'].get('OK_Image_Count', 'N/A')}</p>
                    <p><strong>NG Image Count:</strong> {record['image_counts'].get('NG_Image_Count', 'N/A')}</p>
                    <p><strong>Uncertain Image Count:</strong> {record['image_counts'].get('Uncertain_Image_Count', 'N/A')}</p>
                </div>
                
                <h3>Station詳細資料</h3>
                <div class="data-section">
            """
            
            for station, details in record['station_details'].items():
                html_content += f"""
                    <div class="station-data">
                        <strong>{station}:</strong><br>
                        OK: {', '.join(details.get('OK', []))}<br>
                        NG: {', '.join(details.get('NG', []))}<br>
                        SKIP: {', '.join(details.get('SKIP', []))}
                    </div>
                """
            
            html_content += """
                </div>
            </div>
            """
        
        html_content += """
        </body>
        </html>
        """
        
        report_file = os.path.join(self.requests_dir, f"report_{timestamp}.html")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        self.log(f"HTML報告已儲存: {report_file}")
    
    def run(self):
        """執行完整的爬蟲流程"""
        try:
            self.log("開始執行Requests-HTML爬蟲...")
            
            # 1. 載入輸入資料
            if not self.load_input_data():
                return False
            
            # 2. 登入
            if not self.login():
                self.log("登入失敗，但繼續嘗試抓取...")
            
            # 3. 處理每一筆輸入資料
            for i, data_row in enumerate(self.input_data):
                self.log(f"處理第 {i+1}/{len(self.input_data)} 筆資料")
                
                result = self.scrape_substrate_page(data_row)
                if result:
                    self.all_data.append(result)
                
                # 在處理下一筆資料前稍作休息
                if i < len(self.input_data) - 1:
                    time.sleep(3)
            
            # 4. 儲存資料
            self.save_data()
            
            self.log(f"爬蟲執行完成！成功處理 {len(self.all_data)} 筆資料")
            return True
            
        except Exception as e:
            self.log(f"爬蟲執行失敗: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("Requests-HTML Substrate Dashboard 爬蟲")
    print("無需Chrome WebDriver，自動處理JavaScript")
    print("=" * 60)
    
    # 檢查是否安裝了requests-html
    try:
        from requests_html import HTMLSession
        print("✅ requests-html 已安裝")
    except ImportError:
        print("❌ 請先安裝 requests-html:")
        print("pip install requests-html")
        exit(1)
    
    scraper = RequestsHTMLScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ Requests-HTML爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.requests_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ Requests-HTML爬蟲執行失敗")
    
    print("\n📊 輸出檔案:")
    print("- requests_html_data_*.json (完整JSON資料)")
    print("- requests_html_data_*.csv (CSV格式資料)")
    print("- report_*.html (視覺化報告)")


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 CSV 檔案載入
"""

import pandas as pd
import os

def test_csv_loading():
    """測試CSV檔案載入"""
    print("=" * 60)
    print("測試 input_data.csv 載入")
    print("=" * 60)
    
    csv_file = 'input_data.csv'
    
    # 檢查檔案是否存在
    if not os.path.exists(csv_file):
        print(f"❌ 檔案不存在: {csv_file}")
        return False
    
    print(f"✅ 檔案存在: {csv_file}")
    
    # 檢查檔案大小
    file_size = os.path.getsize(csv_file)
    print(f"檔案大小: {file_size} bytes")
    
    # 讀取原始內容
    print("\n原始檔案內容:")
    print("-" * 40)
    with open(csv_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(repr(content))  # 使用repr顯示特殊字元
    
    print("\n檔案內容 (可讀格式):")
    print("-" * 40)
    with open(csv_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            print(f"第{i}行: {repr(line)}")
    
    # 嘗試用pandas讀取
    print("\n嘗試用 pandas 讀取:")
    print("-" * 40)
    try:
        df = pd.read_csv(csv_file)
        print(f"✅ 成功讀取 CSV")
        print(f"資料筆數: {len(df)}")
        print(f"欄位名稱: {list(df.columns)}")
        print("\n資料內容:")
        print(df)
        
        # 轉換為字典
        input_data = df.to_dict('records')
        print(f"\n轉換為字典: {input_data}")
        
        return True, input_data
        
    except Exception as e:
        print(f"❌ pandas 讀取失敗: {str(e)}")
        
        # 嘗試手動解析
        print("\n嘗試手動解析:")
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            if len(lines) < 2:
                print("❌ 檔案內容不足")
                return False, []
            
            # 解析標題行
            header = lines[0].split(',')
            print(f"標題行: {header}")
            
            # 解析資料行
            data = []
            for i, line in enumerate(lines[1:], 2):
                values = line.split(',')
                if len(values) == len(header):
                    row = dict(zip(header, values))
                    data.append(row)
                    print(f"第{i}行資料: {row}")
                else:
                    print(f"第{i}行格式錯誤: {values}")
            
            print(f"\n手動解析結果: {data}")
            return True, data
            
        except Exception as e2:
            print(f"❌ 手動解析也失敗: {str(e2)}")
            return False, []

def create_correct_csv():
    """建立正確的CSV檔案"""
    print("\n" + "=" * 60)
    print("建立正確的 CSV 檔案")
    print("=" * 60)
    
    # 建立正確的CSV內容
    csv_content = """code,lot,ship
A06-192,92540363,ship001
a06198,92540397,ship002
b07299,93651408,ship003"""
    
    # 儲存到新檔案
    new_file = 'input_data_fixed.csv'
    with open(new_file, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print(f"✅ 已建立正確的CSV檔案: {new_file}")
    
    # 測試新檔案
    try:
        df = pd.read_csv(new_file)
        print(f"✅ 新檔案讀取成功")
        print(f"資料筆數: {len(df)}")
        print("\n新檔案內容:")
        print(df)
        
        # 覆蓋原檔案
        import shutil
        shutil.copy(new_file, 'input_data.csv')
        print(f"✅ 已覆蓋原檔案: input_data.csv")
        
        return True
        
    except Exception as e:
        print(f"❌ 新檔案測試失敗: {str(e)}")
        return False

def main():
    """主要測試函數"""
    print("CSV 檔案載入測試工具")
    
    # 測試原檔案
    success, data = test_csv_loading()
    
    if not success or len(data) == 0:
        print("\n原檔案有問題，建立正確的CSV檔案...")
        if create_correct_csv():
            print("\n重新測試修正後的檔案:")
            success, data = test_csv_loading()
    
    if success and len(data) > 0:
        print(f"\n🎉 CSV檔案正常！共有 {len(data)} 筆資料")
        print("現在可以執行爬蟲了:")
        print("python requests_html_scraper.py")
    else:
        print("\n❌ CSV檔案仍有問題")

if __name__ == "__main__":
    main()
