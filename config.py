# 爬虫配置文件
import os

# 服务器配置
SERVER_CONFIG = {
    'base_url': 'http://10.22.22.144:31539',
    'login_url': 'http://10.22.22.144:31539/login',
    'data_urls': [
        'http://10.22.22.144:31539/substrate/overall',
        'http://10.22.22.144:31539/data',
        # 可以添加更多需要抓取的URL
    ]
}

# 认证信息
AUTH_CONFIG = {
    'username': 'admin',
    'password': 'pega#1234'
}

# 请求配置
REQUEST_CONFIG = {
    'timeout': 30,
    'max_retries': 3,
    'delay_between_requests': 1,  # 秒
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
}

# 输出配置
OUTPUT_CONFIG = {
    'data_dir': 'scraped_data_20250616/data',
    'log_dir': 'scraped_data_20250616/logs',
    'formats': ['json', 'csv', 'html']  # 支持的输出格式
}

# 创建必要的目录
def create_directories():
    """创建必要的目录"""
    for dir_name in [OUTPUT_CONFIG['data_dir'], OUTPUT_CONFIG['log_dir']]:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"创建目录: {dir_name}")
