[{"station": null, "start_date": null, "end_date": null, "filters": {}, "timestamp": "2025-06-16T17:52:15.805082", "url": "http://************:31539/substrate/overall", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:17.891829", "url": "http://************:31539/substrate/overall?station=DF1&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:20.008589", "url": "http://************:31539/substrate/overall?station=DF2&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:22.142145", "url": "http://************:31539/substrate/overall?station=DF3&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:24.240019", "url": "http://************:31539/substrate/overall?station=DF4&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:26.379468", "url": "http://************:31539/substrate/overall?station=DF5&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16"}, "timestamp": "2025-06-16T17:52:28.477791", "url": "http://************:31539/substrate/overall?station=DF6&start_date=2025-06-09&end_date=2025-06-16", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:30.597930", "url": "http://************:31539/substrate/overall?station=DF1&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:31.744939", "url": "http://************:31539/substrate/overall?station=DF2&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:32.860269", "url": "http://************:31539/substrate/overall?station=DF3&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:33.951701", "url": "http://************:31539/substrate/overall?station=DF4&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:35.066698", "url": "http://************:31539/substrate/overall?station=DF5&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}, "timestamp": "2025-06-16T17:52:36.193735", "url": "http://************:31539/substrate/overall?station=DF6&start_date=2025-06-09&end_date=2025-06-16&status=OK", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:37.317934", "url": "http://************:31539/substrate/overall?station=DF1&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:38.419332", "url": "http://************:31539/substrate/overall?station=DF2&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:39.601298", "url": "http://************:31539/substrate/overall?station=DF3&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:40.750761", "url": "http://************:31539/substrate/overall?station=DF4&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:41.870263", "url": "http://************:31539/substrate/overall?station=DF5&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}, "timestamp": "2025-06-16T17:52:43.050927", "url": "http://************:31539/substrate/overall?station=DF6&start_date=2025-06-09&end_date=2025-06-16&status=NG", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:44.208112", "url": "http://************:31539/substrate/overall?station=DF1&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:45.385872", "url": "http://************:31539/substrate/overall?station=DF2&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:46.518383", "url": "http://************:31539/substrate/overall?station=DF3&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:47.654868", "url": "http://************:31539/substrate/overall?station=DF4&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:48.798432", "url": "http://************:31539/substrate/overall?station=DF5&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}, "timestamp": "2025-06-16T17:52:49.970154", "url": "http://************:31539/substrate/overall?station=DF6&start_date=2025-06-09&end_date=2025-06-16&type=AI_Loss", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:51.133716", "url": "http://************:31539/substrate/overall?station=DF1&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:52.231804", "url": "http://************:31539/substrate/overall?station=DF2&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:53.338197", "url": "http://************:31539/substrate/overall?station=DF3&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:54.453522", "url": "http://************:31539/substrate/overall?station=DF4&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:55.548860", "url": "http://************:31539/substrate/overall?station=DF5&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}, "timestamp": "2025-06-16T17:52:56.668454", "url": "http://************:31539/substrate/overall?station=DF6&start_date=2025-06-09&end_date=2025-06-16&type=AI_Overkill", "status_code": 200, "tables": [], "forms": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}]