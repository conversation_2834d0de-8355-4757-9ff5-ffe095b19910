@echo off
echo ========================================
echo Web Scraper for ************:31539
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "venv" (
    echo 虚拟环境不存在，正在创建...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo 创建虚拟环境失败，请确保已安装Python
        pause
        exit /b 1
    )
    echo 虚拟环境创建成功！
    echo.
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查激活是否成功
echo 当前Python路径:
where python

REM 安装依赖
echo.
echo 安装依赖包...
pip install -r requirements.txt

REM 检查安装是否成功
echo.
echo 检查已安装的包:
pip list

REM 运行爬虫
echo.
echo 开始运行爬虫...
echo.
python run_scraper.py

REM 保持窗口打开
echo.
echo 按任意键退出...
pause
