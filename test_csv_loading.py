#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 CSV 檔案載入
"""

import pandas as pd
import os

def test_csv_loading():
    """測試CSV檔案載入"""
    print("=" * 60)
    print("測試 input_data.csv 載入")
    print("=" * 60)
    
    csv_file = 'input_data.csv'
    
    # 檢查檔案是否存在
    if not os.path.exists(csv_file):
        print(f"❌ 檔案不存在: {csv_file}")
        return False
    
    print(f"✅ 檔案存在: {csv_file}")
    
    # 檢查檔案大小
    file_size = os.path.getsize(csv_file)
    print(f"檔案大小: {file_size} bytes")
    
    # 讀取原始內容
    print("\n原始檔案內容:")
    print("-" * 40)
    with open(csv_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(repr(content))  # 使用repr顯示特殊字元
    
    print("\n檔案內容 (可讀格式):")
    print("-" * 40)
    with open(csv_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            print(f"第{i}行: {repr(line)}")
    
    # 嘗試用pandas讀取
    print("\n嘗試用 pandas 讀取:")
    print("-" * 40)
    try:
        df = pd.read_csv(csv_file)
        print(f"✅ 成功讀取 CSV")
        print(f"資料筆數: {len(df)}")
        print(f"欄位名稱: {list(df.columns)}")
        print("\n資料內容:")
        print(df)
        
        # 轉換為字典
        input_data = df.to_dict('records')
        print(f"\n轉換為字典: {input_data}")
        
        return True, input_data
        
    except Exception as e:
        print(f"❌ pandas 讀取失敗: {str(e)}")
        
        # 嘗試手動解析
        print("\n嘗試手動解析:")
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            if len(lines) < 2:
                print("❌ 檔案內容不足")
                return False, []
            
            # 解析標題行
            header = lines[0].split(',')
            print(f"標題行: {header}")
            
            # 解析資料行
            data = []
            for i, line in enumerate(lines[1:], 2):
                values = line.split(',')
                if len(values) == len(header):
                    row = dict(zip(header, values))
                    data.append(row)
                    print(f"第{i}行資料: {row}")
                else:
                    print(f"第{i}行格式錯誤: {values}")
            
            print(f"\n手動解析結果: {data}")
            return True, data
            
        except Exception as e2:
            print(f"❌ 手動解析也失敗: {str(e2)}")
            return False, []

def create_correct_csv():
    """建立正確的CSV檔案"""
    print("\n" + "=" * 60)
    print("建立正確的 CSV 檔案")
    print("=" * 60)
    
    # 建立正確的CSV內容
    csv_content = """code,lot,ship
A06-192,92540363,ship001
a06198,92540397,ship002
b07299,93651408,ship003"""
    
    # 儲存到新檔案
    new_file = 'input_data_fixed.csv'
    with open(new_file, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print(f"✅ 已建立正確的CSV檔案: {new_file}")
    
    # 測試新檔案
    try:
        df = pd.read_csv(new_file)
        print(f"✅ 新檔案讀取成功")
        print(f"資料筆數: {len(df)}")
        print("\n新檔案內容:")
        print(df)
        
        # 覆蓋原檔案
        import shutil
        shutil.copy(new_file, 'input_data.csv')
        print(f"✅ 已覆蓋原檔案: input_data.csv")
        
        return True
        
    except Exception as e:
        print(f"❌ 新檔案測試失敗: {str(e)}")
        return False

def main():
    """主要測試函數"""
    print("CSV 檔案載入測試工具")
    
    # 測試原檔案
    success, data = test_csv_loading()
    
    if not success or len(data) == 0:
        print("\n原檔案有問題，建立正確的CSV檔案...")
        if create_correct_csv():
            print("\n重新測試修正後的檔案:")
            success, data = test_csv_loading()
    
    if success and len(data) > 0:
        print(f"\n🎉 CSV檔案正常！共有 {len(data)} 筆資料")
        print("現在可以執行爬蟲了:")
        print("python requests_html_scraper.py")
    else:
        print("\n❌ CSV檔案仍有問題")

if __name__ == "__main__":
    main()
