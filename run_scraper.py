#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行爬虫的简单脚本
"""

import sys
import os
from web_scraper import WebScraper

def main():
    """主函数"""
    print("=" * 50)
    print("Web Scraper for 10.22.22.144:31539")
    print("=" * 50)
    
    try:
        # 创建爬虫实例
        scraper = WebScraper()
        
        # 运行爬虫
        success = scraper.run()
        
        if success:
            print("\n✅ 爬虫运行成功！")
            print(f"📁 数据保存在: {os.path.abspath('scraped_data')}")
            print(f"📋 日志保存在: {os.path.abspath('logs')}")
        else:
            print("\n❌ 爬虫运行失败，请检查日志文件")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬虫运行")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
