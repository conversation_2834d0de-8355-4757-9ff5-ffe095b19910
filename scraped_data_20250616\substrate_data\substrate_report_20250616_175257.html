
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Substrate Overall 資料報告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .data-section { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
                .section-title { color: #333; border-bottom: 2px solid #007bff; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .station { font-weight: bold; color: #007bff; }
                .timestamp { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>Substrate Overall 資料報告</h1>
            <p>生成時間: 2025-06-16 17:52:57</p>
            <p>共抓取 31 筆資料</p>
        
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">All</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:15.805082</p>
                <p>日期範圍: None ~ None</p>
                <p>篩選條件: {}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF1</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:17.891829</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF2</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:20.008589</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF2', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF3</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:22.142145</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF3', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF4</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:24.240019</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF4', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF5</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:26.379468</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF5', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF6</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:28.477791</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF6', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF1</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:30.597930</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF2</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:31.744939</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF2', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF3</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:32.860269</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF3', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF4</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:33.951701</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF4', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF5</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:35.066698</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF5', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF6</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:36.193735</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF6', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'OK'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF1</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:37.317934</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF2</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:38.419332</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF2', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF3</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:39.601298</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF3', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF4</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:40.750761</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF4', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF5</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:41.870263</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF5', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF6</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:43.050927</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF6', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'status': 'NG'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF1</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:44.208112</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF2</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:45.385872</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF2', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF3</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:46.518383</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF3', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF4</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:47.654868</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF4', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF5</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:48.798432</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF5', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF6</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:49.970154</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF6', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Loss'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF1</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:51.133716</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF2</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:52.231804</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF2', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF3</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:53.338197</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF3', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF4</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:54.453522</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF4', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF5</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:55.548860</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF5', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">DF6</span></h2>
                <p class="timestamp">抓取時間: 2025-06-16T17:52:56.668454</p>
                <p>日期範圍: 2025-06-09 ~ 2025-06-16</p>
                <p>篩選條件: {'station': 'DF6', 'start_date': '2025-06-09', 'end_date': '2025-06-16', 'type': 'AI_Overkill'}</p>
                <p>狀態碼: 200</p>
                <p>表格數量: 0</p>
            </div>
        </body>
        </html>
        