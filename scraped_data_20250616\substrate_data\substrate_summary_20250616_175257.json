[{"station": null, "start_date": null, "end_date": null, "timestamp": "2025-06-16T17:52:15.805082", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {}}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:17.891829", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:20.008589", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:22.142145", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:24.240019", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:26.379468", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:28.477791", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16"}}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:30.597930", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:31.744939", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:32.860269", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:33.951701", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:35.066698", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:36.193735", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "OK"}}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:37.317934", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:38.419332", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:39.601298", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:40.750761", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:41.870263", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:43.050927", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "status": "NG"}}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:44.208112", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:45.385872", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:46.518383", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:47.654868", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:48.798432", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:49.970154", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Loss"}}, {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:51.133716", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF1", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}, {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:52.231804", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF2", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}, {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:53.338197", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF3", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}, {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:54.453522", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF4", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}, {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:55.548860", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF5", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}, {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "timestamp": "2025-06-16T17:52:56.668454", "status_code": 200, "tables_count": 0, "total_rows": 0, "filters": {"station": "DF6", "start_date": "2025-06-09", "end_date": "2025-06-16", "type": "AI_Overkill"}}]