[{"url": "http://************:31539", "timestamp": "2025-06-16T17:46:05.870178", "status_code": 200, "content_length": 503, "text_data": ["PEGA AI"], "links": [], "tables": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"url": "http://************:31539/substrate/overall", "timestamp": "2025-06-16T17:46:06.960143", "status_code": 200, "content_length": 503, "text_data": ["PEGA AI"], "links": [], "tables": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"url": "http://************:31539/data", "timestamp": "2025-06-16T17:46:08.058437", "status_code": 200, "content_length": 503, "text_data": ["PEGA AI"], "links": [], "tables": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"url": "http://************:31539/dashboard", "timestamp": "2025-06-16T17:46:09.200262", "status_code": 200, "content_length": 503, "text_data": ["PEGA AI"], "links": [], "tables": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}, {"url": "http://************:31539/status", "timestamp": "2025-06-16T17:46:10.297591", "status_code": 200, "content_length": 503, "text_data": ["PEGA AI"], "links": [], "tables": [], "raw_html": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n"}]