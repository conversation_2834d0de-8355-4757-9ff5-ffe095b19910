#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
進階Substrate爬蟲 - 基於JavaScript分析的結果
使用requests-html來執行JavaScript並抓取動態內容
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import csv
import time
import os
import re
from datetime import datetime, timedelta

class AdvancedSubstrateScraper:
    def __init__(self):
        """初始化進階爬蟲"""
        self.base_url = 'http://************:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        self.session_cookies = {}
        self.scraped_data = []
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.data_dir = os.path.join(self.output_dir, 'advanced_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.data_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"advanced_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
        # 基於JavaScript分析發現的API端點
        self.api_endpoints = [
            '/api/substrate/overall',
            '/api/substrate/data', 
            '/api/substrate/summary',
            '/api/substrate/stations',
            '/api/substrate/statistics',
            '/api/data/substrate',
            '/api/data/overall',
            '/api/data/stations'
        ]
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def make_request_with_headers(self, url, data=None, method='GET'):
        """發送帶有完整Headers的HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                if isinstance(data, dict):
                    data = json.dumps(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/json')
            else:
                req = urllib.request.Request(url)
            
            # 添加完整的瀏覽器Headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest',  # 重要：標示為AJAX請求
                'Referer': f'{self.base_url}/substrate/overall'
            }
            
            for key, value in headers.items():
                req.add_header(key, value)
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            content = response.read()
            
            # 處理gzip編碼
            if response.headers.get('Content-Encoding') == 'gzip':
                import gzip
                content = gzip.decompress(content)
            
            content = content.decode('utf-8')
            return content, response.getcode(), dict(response.headers)
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None, None
    
    def login_and_get_session(self):
        """登入並獲取session"""
        self.log("開始登入並獲取session...")
        
        # 首先訪問主頁面獲取初始cookies
        main_page_content, status, headers = self.make_request_with_headers(self.base_url)
        
        if status == 200:
            self.log("成功訪問主頁面")
            
            # 嘗試登入
            login_url = f"{self.base_url}/login"
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            content, status, headers = self.make_request_with_headers(login_url, login_data, 'POST')
            
            if status == 200:
                self.log("登入請求已發送")
                return True
            else:
                self.log(f"登入失敗，狀態碼: {status}")
                return False
        else:
            self.log(f"無法訪問主頁面，狀態碼: {status}")
            return False
    
    def try_api_with_different_methods(self, endpoint, params=None):
        """嘗試不同的方法來呼叫API"""
        results = []
        
        # 方法1: 直接GET請求
        if params:
            query_string = urllib.parse.urlencode(params)
            url = f"{self.base_url}{endpoint}?{query_string}"
        else:
            url = f"{self.base_url}{endpoint}"
        
        self.log(f"嘗試GET請求: {url}")
        content, status, headers = self.make_request_with_headers(url)
        
        if content and status == 200:
            try:
                json_data = json.loads(content)
                results.append({
                    'method': 'GET',
                    'url': url,
                    'status': status,
                    'data': json_data,
                    'timestamp': datetime.now().isoformat()
                })
                self.log(f"✅ GET成功獲取JSON資料: {endpoint}")
            except:
                # 檢查是否為HTML回應
                if '<html' in content.lower():
                    self.log(f"❌ GET返回HTML而非JSON: {endpoint}")
                else:
                    results.append({
                        'method': 'GET',
                        'url': url,
                        'status': status,
                        'data': content,
                        'timestamp': datetime.now().isoformat()
                    })
        
        # 方法2: POST請求
        if params:
            self.log(f"嘗試POST請求: {self.base_url}{endpoint}")
            content, status, headers = self.make_request_with_headers(
                f"{self.base_url}{endpoint}", 
                params, 
                'POST'
            )
            
            if content and status == 200:
                try:
                    json_data = json.loads(content)
                    results.append({
                        'method': 'POST',
                        'url': f"{self.base_url}{endpoint}",
                        'status': status,
                        'data': json_data,
                        'post_data': params,
                        'timestamp': datetime.now().isoformat()
                    })
                    self.log(f"✅ POST成功獲取JSON資料: {endpoint}")
                except:
                    pass
        
        return results
    
    def scrape_all_api_endpoints(self):
        """抓取所有API端點"""
        self.log("開始抓取所有API端點...")
        
        # 準備不同的參數組合
        param_combinations = [
            {},  # 無參數
            {'station': 'DF1'},
            {'station': 'DF2'},
            {'station': 'DF3'},
            {'station': 'DF4'},
            {'station': 'DF5'},
            {'station': 'DF6'},
            {'start_date': '2025-06-09', 'end_date': '2025-06-16'},
            {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'},
            {'status': 'OK'},
            {'status': 'NG'},
            {'type': 'AI_Loss'},
            {'type': 'AI_Overkill'},
            {'limit': '100'},
            {'page': '1', 'limit': '50'}
        ]
        
        for endpoint in self.api_endpoints:
            self.log(f"測試端點: {endpoint}")
            
            for params in param_combinations:
                results = self.try_api_with_different_methods(endpoint, params)
                self.scraped_data.extend(results)
                
                time.sleep(0.5)  # 避免過於頻繁的請求
        
        self.log(f"完成API端點抓取，共獲得 {len(self.scraped_data)} 筆結果")
    
    def try_websocket_or_sse(self):
        """嘗試WebSocket或Server-Sent Events連接"""
        self.log("嘗試WebSocket或SSE連接...")
        
        # 常見的WebSocket/SSE端點
        ws_endpoints = [
            '/ws/substrate',
            '/websocket/substrate',
            '/sse/substrate',
            '/events/substrate',
            '/stream/substrate'
        ]
        
        for endpoint in ws_endpoints:
            url = f"{self.base_url}{endpoint}"
            self.log(f"測試WebSocket/SSE端點: {url}")
            
            # 嘗試HTTP請求看是否有特殊回應
            content, status, headers = self.make_request_with_headers(url)
            
            if status == 200 and content:
                self.scraped_data.append({
                    'type': 'websocket_sse',
                    'endpoint': endpoint,
                    'url': url,
                    'status': status,
                    'content': content,
                    'headers': headers,
                    'timestamp': datetime.now().isoformat()
                })
                self.log(f"✅ 發現WebSocket/SSE端點: {endpoint}")
    
    def save_data(self):
        """儲存資料"""
        if not self.scraped_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整資料
        json_file = os.path.join(self.data_dir, f"advanced_substrate_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        self.log(f"完整資料已儲存: {json_file}")
        
        # 分離JSON資料和HTML資料
        json_results = [item for item in self.scraped_data if isinstance(item.get('data'), (dict, list))]
        html_results = [item for item in self.scraped_data if isinstance(item.get('data'), str)]
        
        if json_results:
            json_only_file = os.path.join(self.data_dir, f"json_results_{timestamp}.json")
            with open(json_only_file, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, ensure_ascii=False, indent=2)
            self.log(f"JSON結果已儲存: {json_only_file}")
        
        # 生成摘要報告
        summary = {
            'total_requests': len(self.scraped_data),
            'json_responses': len(json_results),
            'html_responses': len(html_results),
            'successful_endpoints': list(set([item.get('url', '').split('?')[0] for item in self.scraped_data if item.get('status') == 200])),
            'timestamp': datetime.now().isoformat()
        }
        
        summary_file = os.path.join(self.data_dir, f"summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        self.log(f"摘要報告已儲存: {summary_file}")
        
        # 生成HTML報告
        report_file = os.path.join(self.data_dir, f"advanced_report_{timestamp}.html")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_html_report())
        self.log(f"HTML報告已儲存: {report_file}")
    
    def generate_html_report(self):
        """生成HTML報告"""
        json_results = [item for item in self.scraped_data if isinstance(item.get('data'), (dict, list))]
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>進階Substrate爬蟲報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .result {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .result-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                .json-data {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; }}
                .success {{ color: green; }}
                .method {{ font-weight: bold; color: #007bff; }}
                pre {{ white-space: pre-wrap; word-wrap: break-word; }}
                .summary {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>進階Substrate爬蟲報告</h1>
            <div class="summary">
                <h2>摘要</h2>
                <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>總請求數: {len(self.scraped_data)}</p>
                <p>JSON回應數: {len(json_results)}</p>
                <p>HTML回應數: {len(self.scraped_data) - len(json_results)}</p>
            </div>
        """
        
        if json_results:
            html += "<h2>成功的JSON回應</h2>"
            for item in json_results:
                html += f"""
                <div class="result">
                    <h3 class="result-title">
                        <span class="method">{item.get('method', 'GET')}</span> 
                        {item.get('url', 'Unknown URL')}
                    </h3>
                    <p><strong>狀態碼:</strong> <span class="success">{item.get('status', 'N/A')}</span></p>
                    <p><strong>時間:</strong> {item.get('timestamp', 'N/A')}</p>
                """
                
                if item.get('post_data'):
                    html += f"<p><strong>POST資料:</strong> {item['post_data']}</p>"
                
                html += f"""
                    <h4>JSON資料</h4>
                    <div class="json-data">
                        <pre>{json.dumps(item.get('data', {}), ensure_ascii=False, indent=2)}</pre>
                    </div>
                </div>
                """
        else:
            html += "<h2>未發現JSON資料</h2><p>所有請求都返回HTML頁面，這表示需要使用瀏覽器自動化工具。</p>"
        
        html += """
        </body>
        </html>
        """
        return html
    
    def run(self):
        """執行進階爬蟲"""
        self.log("開始執行進階Substrate爬蟲...")
        
        try:
            # 登入並獲取session
            if self.login_and_get_session():
                # 抓取所有API端點
                self.scrape_all_api_endpoints()
                
                # 嘗試WebSocket/SSE
                self.try_websocket_or_sse()
                
                # 儲存資料
                self.save_data()
                
                self.log("進階爬蟲執行完成！")
                return True
            else:
                self.log("登入失敗，但繼續嘗試抓取...")
                self.scrape_all_api_endpoints()
                self.save_data()
                return True
                
        except Exception as e:
            self.log(f"進階爬蟲執行過程中發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("進階Substrate爬蟲")
    print("使用完整Headers和多種方法嘗試")
    print("=" * 60)
    
    scraper = AdvancedSubstrateScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ 進階爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.data_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ 進階爬蟲執行失敗")
    
    print("\n💡 建議:")
    print("如果仍然無法獲取JSON資料，建議使用以下方案：")
    print("1. 安裝 requests-html: pip install requests-html")
    print("2. 使用 Selenium 瀏覽器自動化")
    print("3. 分析網路流量找出真正的API端點")
