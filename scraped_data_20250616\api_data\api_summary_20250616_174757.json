[{"endpoint": "/api", "url": "http://************:31539/api", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:37.107231", "has_json": false}, {"endpoint": "/api/v1", "url": "http://************:31539/api/v1", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:37.974813", "has_json": false}, {"endpoint": "/api/v2", "url": "http://************:31539/api/v2", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:39.144439", "has_json": false}, {"endpoint": "/api/status", "url": "http://************:31539/api/status", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:39.974563", "has_json": false}, {"endpoint": "/api/health", "url": "http://************:31539/api/health", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:40.781154", "has_json": false}, {"endpoint": "/api/info", "url": "http://************:31539/api/info", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:41.509297", "has_json": false}, {"endpoint": "/api/data", "url": "http://************:31539/api/data", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:42.275205", "has_json": false}, {"endpoint": "/api/substrate", "url": "http://************:31539/api/substrate", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:43.314014", "has_json": false}, {"endpoint": "/api/substrate/overall", "url": "http://************:31539/api/substrate/overall", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:44.489318", "has_json": false}, {"endpoint": "/api/dashboard", "url": "http://************:31539/api/dashboard", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:45.673264", "has_json": false}, {"endpoint": "/api/metrics", "url": "http://************:31539/api/metrics", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:46.637718", "has_json": false}, {"endpoint": "/api/stats", "url": "http://************:31539/api/stats", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:47.433321", "has_json": false}, {"endpoint": "/health", "url": "http://************:31539/health", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:48.200718", "has_json": false}, {"endpoint": "/status", "url": "http://************:31539/status", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:48.800421", "has_json": false}, {"endpoint": "/info", "url": "http://************:31539/info", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:49.555433", "has_json": false}, {"endpoint": "/metrics", "url": "http://************:31539/metrics", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:50.299990", "has_json": false}, {"endpoint": "/version", "url": "http://************:31539/version", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:50.989522", "has_json": false}, {"endpoint": "/ping", "url": "http://************:31539/ping", "status": 200, "content_length": 0, "content_type": "", "timestamp": "2025-06-16T17:47:51.613770", "has_json": false}, {"endpoint": "/js/main.e7ca49.js", "url": "http://************:31539/js/main.e7ca49.js", "status": 200, "content_length": 3504943, "content_type": "text/javascript; charset=utf-8", "timestamp": "2025-06-16T17:47:53.034547", "has_json": false}, {"endpoint": "/api/substrate/overall", "url": "http://************:31539/api/substrate/overall", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:54.313319", "has_json": false}, {"endpoint": "/api/data", "url": "http://************:31539/api/data", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:54.882354", "has_json": false}, {"endpoint": "/api/dashboard", "url": "http://************:31539/api/dashboard", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:55.466749", "has_json": false}, {"endpoint": "/substrate/overall", "url": "http://************:31539/substrate/overall", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:56.055006", "has_json": false}, {"endpoint": "/data", "url": "http://************:31539/data", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:56.634893", "has_json": false}, {"endpoint": "/dashboard", "url": "http://************:31539/dashboard", "status": 200, "content_length": 503, "content_type": "text/html", "timestamp": "2025-06-16T17:47:57.207899", "has_json": false}]