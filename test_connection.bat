@echo off
echo ========================================
echo 测试服务器连接
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "venv" (
    echo 虚拟环境不存在，正在创建...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo 创建虚拟环境失败，请确保已安装Python
        pause
        exit /b 1
    )
    echo 虚拟环境创建成功！
    echo.
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查激活是否成功
echo 当前Python路径:
where python
echo.

REM 安装依赖
echo 安装依赖包...
pip install -r requirements.txt
echo.

REM 运行连接测试
echo 开始测试连接...
echo.
python test_connection.py

REM 保持窗口打开
echo.
pause
