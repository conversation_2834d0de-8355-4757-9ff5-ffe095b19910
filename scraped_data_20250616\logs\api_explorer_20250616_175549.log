[2025-06-16 17:55:49] 開始API探索...
[2025-06-16 17:55:49] 分析JavaScript檔案...
[2025-06-16 17:55:50] 成功獲取JavaScript檔案，大小: 3504943 字元
[2025-06-16 17:55:50] 從JavaScript中發現 45 個潛在端點
[2025-06-16 17:55:50] JavaScript內容已儲存: scraped_data_20250616\api_exploration\main_js_content.txt
[2025-06-16 17:55:50] 探索常見API端點...
[2025-06-16 17:55:50] 測試端點: /api/substrate/overall
[2025-06-16 17:55:51] ✅ 發現有效端點: /api/substrate/overall
[2025-06-16 17:55:51] 測試端點: /api/substrate/data
[2025-06-16 17:55:51] ✅ 發現有效端點: /api/substrate/data
[2025-06-16 17:55:52] 測試端點: /api/substrate/summary
[2025-06-16 17:55:52] ✅ 發現有效端點: /api/substrate/summary
[2025-06-16 17:55:52] 測試端點: /api/substrate/stations
[2025-06-16 17:55:52] ✅ 發現有效端點: /api/substrate/stations
[2025-06-16 17:55:53] 測試端點: /api/substrate/statistics
[2025-06-16 17:55:53] ✅ 發現有效端點: /api/substrate/statistics
[2025-06-16 17:55:53] 測試端點: /api/data/substrate
[2025-06-16 17:55:53] ✅ 發現有效端點: /api/data/substrate
[2025-06-16 17:55:54] 測試端點: /api/data/overall
[2025-06-16 17:55:54] ✅ 發現有效端點: /api/data/overall
[2025-06-16 17:55:55] 測試端點: /api/data/stations
[2025-06-16 17:55:55] ✅ 發現有效端點: /api/data/stations
[2025-06-16 17:55:55] 測試端點: /api/dashboard/data
[2025-06-16 17:55:55] ✅ 發現有效端點: /api/dashboard/data
[2025-06-16 17:55:56] 測試端點: /api/dashboard/substrate
[2025-06-16 17:55:56] ✅ 發現有效端點: /api/dashboard/substrate
[2025-06-16 17:55:56] 測試端點: /api/statistics
[2025-06-16 17:55:56] ✅ 發現有效端點: /api/statistics
[2025-06-16 17:55:57] 測試端點: /api/reports
[2025-06-16 17:55:57] ✅ 發現有效端點: /api/reports
[2025-06-16 17:55:58] 測試端點: /api/export
[2025-06-16 17:55:58] ✅ 發現有效端點: /api/export
[2025-06-16 17:55:58] 測試端點: /substrate/api/overall
[2025-06-16 17:55:58] ✅ 發現有效端點: /substrate/api/overall
[2025-06-16 17:55:59] 測試端點: /substrate/api/data
[2025-06-16 17:55:59] ✅ 發現有效端點: /substrate/api/data
[2025-06-16 17:55:59] 測試端點: /substrate/api/summary
[2025-06-16 17:55:59] ✅ 發現有效端點: /substrate/api/summary
[2025-06-16 17:56:00] 測試端點: /data/api/substrate
[2025-06-16 17:56:00] ✅ 發現有效端點: /data/api/substrate
[2025-06-16 17:56:01] 測試端點: /data/api/overall
[2025-06-16 17:56:01] ✅ 發現有效端點: /data/api/overall
[2025-06-16 17:56:01] 測試端點: /backend/api/substrate
[2025-06-16 17:56:01] ✅ 發現有效端點: /backend/api/substrate
[2025-06-16 17:56:02] 測試端點: /backend/api/data
[2025-06-16 17:56:02] ✅ 發現有效端點: /backend/api/data
[2025-06-16 17:56:02] 測試端點: /v1/substrate/overall
[2025-06-16 17:56:02] ✅ 發現有效端點: /v1/substrate/overall
[2025-06-16 17:56:03] 測試端點: /v1/substrate/data
[2025-06-16 17:56:03] ✅ 發現有效端點: /v1/substrate/data
[2025-06-16 17:56:03] 測試端點: /v1/data/substrate
[2025-06-16 17:56:04] ✅ 發現有效端點: /v1/data/substrate
[2025-06-16 17:56:04] 測試端點: /v2/substrate/overall
[2025-06-16 17:56:04] ✅ 發現有效端點: /v2/substrate/overall
[2025-06-16 17:56:05] 測試端點: /v2/substrate/data
[2025-06-16 17:56:05] ✅ 發現有效端點: /v2/substrate/data
[2025-06-16 17:56:05] 測試substrate端點的參數組合...
[2025-06-16 17:56:05] 測試: /api/substrate/overall 參數: {}
[2025-06-16 17:56:06] 測試: /api/substrate/overall 參數: {'station': 'DF1'}
[2025-06-16 17:56:06] 測試: /api/substrate/overall 參數: {'station': 'DF2'}
[2025-06-16 17:56:06] 測試: /api/substrate/overall 參數: {'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:07] 測試: /api/substrate/overall 參數: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:07] 測試: /api/substrate/overall 參數: {'status': 'OK'}
[2025-06-16 17:56:08] 測試: /api/substrate/overall 參數: {'status': 'NG'}
[2025-06-16 17:56:08] 測試: /api/substrate/overall 參數: {'type': 'AI_Loss'}
[2025-06-16 17:56:08] 測試: /api/substrate/overall 參數: {'type': 'AI_Overkill'}
[2025-06-16 17:56:09] 測試: /api/substrate/overall 參數: {'limit': '100'}
[2025-06-16 17:56:09] 測試: /api/substrate/overall 參數: {'page': '1', 'limit': '50'}
[2025-06-16 17:56:09] 測試: /substrate/overall 參數: {}
[2025-06-16 17:56:10] 測試: /substrate/overall 參數: {'station': 'DF1'}
[2025-06-16 17:56:10] 測試: /substrate/overall 參數: {'station': 'DF2'}
[2025-06-16 17:56:11] 測試: /substrate/overall 參數: {'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:11] 測試: /substrate/overall 參數: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:11] 測試: /substrate/overall 參數: {'status': 'OK'}
[2025-06-16 17:56:12] 測試: /substrate/overall 參數: {'status': 'NG'}
[2025-06-16 17:56:12] 測試: /substrate/overall 參數: {'type': 'AI_Loss'}
[2025-06-16 17:56:12] 測試: /substrate/overall 參數: {'type': 'AI_Overkill'}
[2025-06-16 17:56:13] 測試: /substrate/overall 參數: {'limit': '100'}
[2025-06-16 17:56:13] 測試: /substrate/overall 參數: {'page': '1', 'limit': '50'}
[2025-06-16 17:56:14] 測試: /api/data 參數: {}
[2025-06-16 17:56:14] 測試: /api/data 參數: {'station': 'DF1'}
[2025-06-16 17:56:14] 測試: /api/data 參數: {'station': 'DF2'}
[2025-06-16 17:56:15] 測試: /api/data 參數: {'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:15] 測試: /api/data 參數: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:15] 測試: /api/data 參數: {'status': 'OK'}
[2025-06-16 17:56:16] 測試: /api/data 參數: {'status': 'NG'}
[2025-06-16 17:56:16] 測試: /api/data 參數: {'type': 'AI_Loss'}
[2025-06-16 17:56:17] 測試: /api/data 參數: {'type': 'AI_Overkill'}
[2025-06-16 17:56:17] 測試: /api/data 參數: {'limit': '100'}
[2025-06-16 17:56:17] 測試: /api/data 參數: {'page': '1', 'limit': '50'}
[2025-06-16 17:56:18] 測試: /data 參數: {}
[2025-06-16 17:56:18] 測試: /data 參數: {'station': 'DF1'}
[2025-06-16 17:56:19] 測試: /data 參數: {'station': 'DF2'}
[2025-06-16 17:56:19] 測試: /data 參數: {'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:19] 測試: /data 參數: {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'}
[2025-06-16 17:56:20] 測試: /data 參數: {'status': 'OK'}
[2025-06-16 17:56:20] 測試: /data 參數: {'status': 'NG'}
[2025-06-16 17:56:20] 測試: /data 參數: {'type': 'AI_Loss'}
[2025-06-16 17:56:21] 測試: /data 參數: {'type': 'AI_Overkill'}
[2025-06-16 17:56:21] 測試: /data 參數: {'limit': '100'}
[2025-06-16 17:56:21] 測試: /data 參數: {'page': '1', 'limit': '50'}
[2025-06-16 17:56:22] 嘗試POST請求...
[2025-06-16 17:56:22] POST測試: /api/substrate/query
[2025-06-16 17:56:22] POST測試: /api/substrate/query
[2025-06-16 17:56:23] POST測試: /api/substrate/query
[2025-06-16 17:56:23] POST測試: /api/substrate/query
[2025-06-16 17:56:24] POST測試: /api/substrate/search
[2025-06-16 17:56:25] POST測試: /api/substrate/search
[2025-06-16 17:56:25] POST測試: /api/substrate/search
[2025-06-16 17:56:26] POST測試: /api/substrate/search
[2025-06-16 17:56:26] POST測試: /api/data/query
[2025-06-16 17:56:27] POST測試: /api/data/query
[2025-06-16 17:56:27] POST測試: /api/data/query
[2025-06-16 17:56:28] POST測試: /api/data/query
[2025-06-16 17:56:29] POST測試: /api/data/search
[2025-06-16 17:56:29] POST測試: /api/data/search
[2025-06-16 17:56:30] POST測試: /api/data/search
[2025-06-16 17:56:30] POST測試: /api/data/search
[2025-06-16 17:56:31] POST測試: /substrate/query
[2025-06-16 17:56:31] POST測試: /substrate/query
[2025-06-16 17:56:32] POST測試: /substrate/query
[2025-06-16 17:56:32] POST測試: /substrate/query
[2025-06-16 17:56:33] POST測試: /data/query
[2025-06-16 17:56:34] POST測試: /data/query
[2025-06-16 17:56:34] POST測試: /data/query
[2025-06-16 17:56:35] POST測試: /data/query
[2025-06-16 17:56:35] API發現資料已儲存: scraped_data_20250616\api_exploration\api_discoveries_20250616_175635.json
[2025-06-16 17:56:35] API摘要已儲存: scraped_data_20250616\api_exploration\api_summary_20250616_175635.json
[2025-06-16 17:56:35] API報告已儲存: scraped_data_20250616\api_exploration\api_report_20250616_175635.html
[2025-06-16 17:56:35] API探索完成！共發現 25 個有效端點
