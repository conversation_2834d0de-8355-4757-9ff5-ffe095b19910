# Web Scraper for ************:31539

这是一个专门用于抓取 `************:31539` 服务器数据的爬虫工具。

## 功能特点

- 🔐 自动登录功能（用户名：admin，密码：pega#1234）
- 📊 支持多种数据格式输出（JSON、CSV、HTML）
- 🕷️ 两种爬虫模式：基础模式（requests）和高级模式（Selenium）
- 📝 详细的日志记录
- 🛡️ 错误处理和重试机制

## 文件结构

```
├── config.py              # 配置文件
├── web_scraper.py         # 基础爬虫（使用requests）
├── advanced_scraper.py    # 高级爬虫（使用Selenium）
├── run_scraper.py         # 运行脚本
├── run_scraper.bat        # Windows批处理文件
├── requirements.txt       # Python依赖包
└── README.md             # 说明文档
```

## 快速开始

### 方法1：使用批处理文件（推荐）

1. 双击运行 `run_scraper.bat`
2. 脚本会自动：
   - 创建虚拟环境
   - 安装依赖包
   - 运行爬虫

### 方法2：手动运行

1. **创建虚拟环境**
   ```bash
   python -m venv venv
   ```

2. **激活虚拟环境**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行基础爬虫**
   ```bash
   python run_scraper.py
   ```

5. **运行高级爬虫（需要Chrome浏览器）**
   ```bash
   python advanced_scraper.py
   ```

## 配置说明

### 服务器配置 (config.py)

- `base_url`: 服务器基础URL
- `login_url`: 登录页面URL
- `data_urls`: 需要抓取的页面URL列表

### 认证配置

- `username`: 登录用户名（默认：admin）
- `password`: 登录密码（默认：pega#1234）

### 输出配置

- `data_dir`: 数据保存目录（默认：scraped_data）
- `log_dir`: 日志保存目录（默认：logs）
- `formats`: 输出格式（JSON、CSV、HTML）

## 输出文件

### 数据文件
- `scraped_data/scraped_data_YYYYMMDD_HHMMSS.json` - JSON格式数据
- `scraped_data/scraped_data_YYYYMMDD_HHMMSS.csv` - CSV格式数据
- `scraped_data/scraped_data_YYYYMMDD_HHMMSS.html` - HTML格式报告

### 日志文件
- `logs/scraper_YYYYMMDD_HHMMSS.log` - 详细运行日志

## 两种爬虫模式对比

| 特性 | 基础爬虫 (requests) | 高级爬虫 (Selenium) |
|------|-------------------|-------------------|
| 速度 | 快 | 较慢 |
| JavaScript支持 | 不支持 | 支持 |
| 资源占用 | 低 | 高 |
| 浏览器依赖 | 无 | 需要Chrome |
| 适用场景 | 静态页面 | 动态页面 |

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认服务器是否可访问
   - 查看日志文件获取详细错误信息

2. **Chrome驱动问题（高级爬虫）**
   - 确保已安装Chrome浏览器
   - 下载对应版本的ChromeDriver
   - 将ChromeDriver添加到系统PATH

3. **网络连接问题**
   - 检查网络连接
   - 确认服务器地址是否正确
   - 检查防火墙设置

### 日志查看

所有运行日志都保存在 `logs/` 目录中，包含详细的错误信息和运行状态。

## 自定义配置

如需修改抓取的URL或其他配置，请编辑 `config.py` 文件：

```python
# 添加新的抓取URL
SERVER_CONFIG['data_urls'].append('http://************:31539/new_page')

# 修改输出格式
OUTPUT_CONFIG['formats'] = ['json']  # 只输出JSON格式
```

## 注意事项

- 请遵守网站的robots.txt和使用条款
- 适当设置请求间隔，避免对服务器造成过大压力
- 定期检查和更新爬虫代码以适应网站变化
- 妥善保管登录凭据，不要在代码中硬编码敏感信息

## 技术支持

如遇到问题，请查看日志文件或联系技术支持。
