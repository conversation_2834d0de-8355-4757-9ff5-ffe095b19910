[{"type": "websocket_sse", "endpoint": "/ws/substrate", "url": "http://************:31539/ws/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 10:11:57 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T18:10:52.251797"}, {"type": "websocket_sse", "endpoint": "/websocket/substrate", "url": "http://************:31539/websocket/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 10:11:57 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T18:10:52.403387"}, {"type": "websocket_sse", "endpoint": "/sse/substrate", "url": "http://************:31539/sse/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 10:11:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T18:10:52.556048"}, {"type": "websocket_sse", "endpoint": "/events/substrate", "url": "http://************:31539/events/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 10:11:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T18:10:52.720465"}, {"type": "websocket_sse", "endpoint": "/stream/substrate", "url": "http://************:31539/stream/substrate", "status": 200, "content": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n    <meta HTTP-EQUIV=\"Pragma\" CONTENT=\"no-cache\" />\n    <meta HTTP-EQUIV=\"Expires\" CONTENT=\"-1\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\" />\n    <title>PEGA AI</title>\n<link rel=\"icon\" href=\"/favicon.ico\"><script defer src=\"/js/main.e7ca49.js\"></script></head>\n\n<body>\n    <div id=\"app\"></div>\n</body>\n\n</html>\n", "headers": {"Server": "nginx/1.16.1", "Date": "Mon, 16 Jun 2025 10:11:58 GMT", "Content-Type": "text/html", "Content-Length": "503", "Connection": "close", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "", "Access-Control-Allow-Methods": "POST, OPTIONS, GET, PUT, DELETE, PATCH", "Access-Control-Allow-Origin": "", "Access-Control-Expose-Headers": "Content-Disposition"}, "timestamp": "2025-06-16T18:10:52.848505"}]