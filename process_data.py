import pandas as pd

# 讀取 Excel 檔案
df = pd.read_excel('w9_w13_by_ppc.xlsx')

# 顯示資料的基本資訊
print("資料形狀:", df.shape)
print("\n前5筆資料:")
print(df.head())
print("\n資料欄位:")
print(df.columns.tolist())
print("\n資料類型:")
print(df.dtypes)

# 按照 week 和 ppc 分組，並計算各類型的數量
result = df.groupby(['week', 'ppc']).agg({
    'OPNGAING': 'count',
    'OPNGAIOK': 'count',
    'OPOKAIOK': 'count',
    'OPOKAING': 'count'
}).reset_index()

# 將結果儲存為 CSV 檔案
result.to_csv('output.csv', index=False)

print("處理完成！結果已儲存到 output.csv") 

# 讀取另一個 Excel 檔案
df2 = pd.read_excel('w9_w13_by_ppc.xlsx')

# 合併兩個 DataFrame
merged_df = pd.merge(df, df2, on=['week', 'ppc'], how='left')

