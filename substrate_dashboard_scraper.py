#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Substrate Dashboard 專用爬蟲
處理特定輸入資料並抓取所有Station的詳細統計數據
"""

import pandas as pd
import json
import csv
import time
import os
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class SubstrateDashboardScraper:
    def __init__(self):
        """初始化Dashboard爬蟲"""
        self.base_url = 'http://10.22.22.144:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.dashboard_dir = os.path.join(self.output_dir, 'dashboard_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.dashboard_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"dashboard_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
        # 儲存所有抓取的資料
        self.all_data = []
        self.input_data = []
        
        # 設定Chrome選項
        self.chrome_options = Options()
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        # 如果不想看到瀏覽器視窗，取消下面這行的註解
        # self.chrome_options.add_argument('--headless')
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def load_input_data(self, csv_file='input_data.csv'):
        """載入輸入資料"""
        try:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                self.input_data = df.to_dict('records')
                self.log(f"成功載入 {len(self.input_data)} 筆輸入資料")
                return True
            else:
                # 建立範例CSV檔案
                sample_data = [
                    {'code': 'a06198', 'lot': '92540397', 'ship': 'example_ship'},
                    {'code': 'b07299', 'lot': '93651408', 'ship': 'example_ship2'},
                    {'code': 'c08300', 'lot': '94762519', 'ship': 'example_ship3'}
                ]
                df = pd.DataFrame(sample_data)
                df.to_csv(csv_file, index=False)
                self.log(f"建立範例檔案: {csv_file}")
                self.input_data = sample_data
                return True
        except Exception as e:
            self.log(f"載入輸入資料失敗: {str(e)}")
            return False
    
    def setup_driver(self):
        """設定WebDriver"""
        try:
            self.driver = webdriver.Chrome(options=self.chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            self.log("WebDriver設定成功")
            return True
        except Exception as e:
            self.log(f"WebDriver設定失敗: {str(e)}")
            return False
    
    def login(self):
        """登入系統"""
        try:
            self.log("開始登入...")
            self.driver.get(self.base_url)
            
            # 等待登入頁面載入
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = self.driver.find_element(By.NAME, "password")
            
            username_field.clear()
            username_field.send_keys(self.username)
            password_field.clear()
            password_field.send_keys(self.password)
            
            # 點擊登入按鈕
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # 等待登入完成
            self.wait.until(EC.presence_of_element_located((By.ID, "app")))
            self.log("登入成功")
            return True
            
        except Exception as e:
            self.log(f"登入失敗: {str(e)}")
            return False
    
    def navigate_to_substrate_dashboard(self):
        """導航到Substrate Dashboard"""
        try:
            self.log("導航到Substrate Dashboard...")
            self.driver.get(f"{self.base_url}/substrate/overall")
            
            # 等待頁面載入
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(3)  # 額外等待JavaScript執行
            
            self.log("成功到達Dashboard頁面")
            return True
            
        except Exception as e:
            self.log(f"導航失敗: {str(e)}")
            return False
    
    def input_search_criteria(self, data_row):
        """輸入搜尋條件"""
        try:
            self.log(f"輸入搜尋條件: {data_row}")
            
            # 尋找並填入各種輸入欄位
            # 根據您提供的截圖，可能有以下欄位：
            
            # Code欄位 (如 a06198)
            if 'code' in data_row:
                try:
                    code_field = self.driver.find_element(By.XPATH, "//input[@placeholder='Search...' or contains(@name, 'code') or contains(@id, 'code')]")
                    code_field.clear()
                    code_field.send_keys(str(data_row['code']))
                    self.log(f"輸入Code: {data_row['code']}")
                except:
                    self.log("未找到Code輸入欄位")
            
            # Lot欄位 (如 92540397)
            if 'lot' in data_row:
                try:
                    lot_field = self.driver.find_element(By.XPATH, "//input[contains(@name, 'lot') or contains(@id, 'lot')]")
                    lot_field.clear()
                    lot_field.send_keys(str(data_row['lot']))
                    self.log(f"輸入Lot: {data_row['lot']}")
                except:
                    self.log("未找到Lot輸入欄位")
            
            # Ship欄位
            if 'ship' in data_row:
                try:
                    ship_field = self.driver.find_element(By.XPATH, "//input[contains(@name, 'ship') or contains(@id, 'ship')]")
                    ship_field.clear()
                    ship_field.send_keys(str(data_row['ship']))
                    self.log(f"輸入Ship: {data_row['ship']}")
                except:
                    self.log("未找到Ship輸入欄位")
            
            # 點擊Submit按鈕
            try:
                submit_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Submit') or contains(@class, 'submit')]")
                submit_button.click()
                self.log("點擊Submit按鈕")
                time.sleep(3)  # 等待資料載入
                return True
            except:
                self.log("未找到Submit按鈕")
                return False
                
        except Exception as e:
            self.log(f"輸入搜尋條件失敗: {str(e)}")
            return False
    
    def extract_image_counts(self):
        """抓取圖片計數資料"""
        try:
            self.log("開始抓取圖片計數...")
            
            # 抓取三種主要計數
            counts_data = {}
            
            # OK Image Count
            try:
                ok_element = self.driver.find_element(By.XPATH, "//div[contains(text(), 'OK Image Count')]/following-sibling::div | //span[contains(text(), 'OK Image Count')]/following-sibling::span")
                ok_count = ok_element.text.strip()
                counts_data['OK_Image_Count'] = ok_count
                self.log(f"OK Image Count: {ok_count}")
            except:
                counts_data['OK_Image_Count'] = "0"
                self.log("未找到OK Image Count")
            
            # NG Image Count
            try:
                ng_element = self.driver.find_element(By.XPATH, "//div[contains(text(), 'NG Image Count')]/following-sibling::div | //span[contains(text(), 'NG Image Count')]/following-sibling::span")
                ng_count = ng_element.text.strip()
                counts_data['NG_Image_Count'] = ng_count
                self.log(f"NG Image Count: {ng_count}")
            except:
                counts_data['NG_Image_Count'] = "0"
                self.log("未找到NG Image Count")
            
            # Uncertain Image Count
            try:
                uncertain_element = self.driver.find_element(By.XPATH, "//div[contains(text(), 'Uncertain Image Count')]/following-sibling::div | //span[contains(text(), 'Uncertain Image Count')]/following-sibling::span")
                uncertain_count = uncertain_element.text.strip()
                counts_data['Uncertain_Image_Count'] = uncertain_count
                self.log(f"Uncertain Image Count: {uncertain_count}")
            except:
                counts_data['Uncertain_Image_Count'] = "0"
                self.log("未找到Uncertain Image Count")
            
            return counts_data
            
        except Exception as e:
            self.log(f"抓取圖片計數失敗: {str(e)}")
            return {}
    
    def extract_station_details(self):
        """抓取各Station的詳細數據"""
        try:
            self.log("開始抓取Station詳細數據...")
            
            station_data = {}
            
            for station in self.stations:
                self.log(f"抓取 {station} 的數據...")
                station_data[station] = {}
                
                # 嘗試找到該Station的數據區域
                try:
                    # 方法1: 尋找包含Station名稱的元素
                    station_section = self.driver.find_element(By.XPATH, f"//div[contains(text(), '{station}') or contains(@data-station, '{station}')]")
                    
                    # 在該區域內尋找OK/NG/SKIP數據
                    parent_element = station_section.find_element(By.XPATH, "./ancestor::div[contains(@class, 'station') or contains(@class, 'card')]")
                    
                    # 抓取OK數據
                    try:
                        ok_elements = parent_element.find_elements(By.XPATH, ".//span[contains(text(), 'OK') or contains(@class, 'ok')]")
                        station_data[station]['OK'] = [elem.text for elem in ok_elements]
                    except:
                        station_data[station]['OK'] = []
                    
                    # 抓取NG數據
                    try:
                        ng_elements = parent_element.find_elements(By.XPATH, ".//span[contains(text(), 'NG') or contains(@class, 'ng')]")
                        station_data[station]['NG'] = [elem.text for elem in ng_elements]
                    except:
                        station_data[station]['NG'] = []
                    
                    # 抓取SKIP數據
                    try:
                        skip_elements = parent_element.find_elements(By.XPATH, ".//span[contains(text(), 'Skip') or contains(@class, 'skip')]")
                        station_data[station]['SKIP'] = [elem.text for elem in skip_elements]
                    except:
                        station_data[station]['SKIP'] = []
                    
                except:
                    # 方法2: 嘗試從表格中抓取
                    try:
                        table_rows = self.driver.find_elements(By.XPATH, f"//tr[td[contains(text(), '{station}')]]")
                        for row in table_rows:
                            cells = row.find_elements(By.TAG_NAME, "td")
                            if len(cells) >= 3:
                                station_data[station] = {
                                    'data': [cell.text for cell in cells]
                                }
                    except:
                        self.log(f"無法找到 {station} 的數據")
                        station_data[station] = {'OK': [], 'NG': [], 'SKIP': []}
            
            return station_data
            
        except Exception as e:
            self.log(f"抓取Station詳細數據失敗: {str(e)}")
            return {}
    
    def scrape_single_input(self, data_row):
        """處理單一輸入資料的完整抓取流程"""
        try:
            self.log(f"開始處理: {data_row}")
            
            # 1. 輸入搜尋條件
            if not self.input_search_criteria(data_row):
                return None
            
            # 2. 抓取圖片計數
            image_counts = self.extract_image_counts()
            
            # 3. 抓取Station詳細數據
            station_details = self.extract_station_details()
            
            # 4. 組合資料
            result = {
                'input_data': data_row,
                'timestamp': datetime.now().isoformat(),
                'image_counts': image_counts,
                'station_details': station_details
            }
            
            self.log(f"成功抓取資料: {len(station_details)} 個Station")
            return result
            
        except Exception as e:
            self.log(f"處理單一輸入失敗: {str(e)}")
            return None
    
    def save_data(self):
        """儲存所有資料"""
        if not self.all_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整JSON資料
        json_file = os.path.join(self.dashboard_dir, f"dashboard_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.all_data, f, ensure_ascii=False, indent=2)
        self.log(f"完整資料已儲存: {json_file}")
        
        # 轉換為CSV格式
        csv_data = []
        for record in self.all_data:
            base_row = {
                'input_code': record['input_data'].get('code', ''),
                'input_lot': record['input_data'].get('lot', ''),
                'input_ship': record['input_data'].get('ship', ''),
                'timestamp': record['timestamp'],
                'ok_image_count': record['image_counts'].get('OK_Image_Count', ''),
                'ng_image_count': record['image_counts'].get('NG_Image_Count', ''),
                'uncertain_image_count': record['image_counts'].get('Uncertain_Image_Count', '')
            }
            
            # 添加每個Station的詳細數據
            for station, details in record['station_details'].items():
                station_row = base_row.copy()
                station_row['station'] = station
                station_row['station_ok'] = str(details.get('OK', []))
                station_row['station_ng'] = str(details.get('NG', []))
                station_row['station_skip'] = str(details.get('SKIP', []))
                csv_data.append(station_row)
        
        # 儲存CSV檔案
        csv_file = os.path.join(self.dashboard_dir, f"dashboard_data_{timestamp}.csv")
        if csv_data:
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False)
            self.log(f"CSV資料已儲存: {csv_file}")
        
        # 生成摘要報告
        summary = {
            'total_inputs_processed': len(self.all_data),
            'total_stations': len(self.stations),
            'processing_time': timestamp,
            'successful_extractions': len([d for d in self.all_data if d['image_counts']])
        }
        
        summary_file = os.path.join(self.dashboard_dir, f"summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        self.log(f"摘要報告已儲存: {summary_file}")
    
    def run(self):
        """執行完整的爬蟲流程"""
        try:
            self.log("開始執行Substrate Dashboard爬蟲...")
            
            # 1. 載入輸入資料
            if not self.load_input_data():
                return False
            
            # 2. 設定WebDriver
            if not self.setup_driver():
                return False
            
            # 3. 登入
            if not self.login():
                return False
            
            # 4. 導航到Dashboard
            if not self.navigate_to_substrate_dashboard():
                return False
            
            # 5. 處理每一筆輸入資料
            for i, data_row in enumerate(self.input_data):
                self.log(f"處理第 {i+1}/{len(self.input_data)} 筆資料")
                
                result = self.scrape_single_input(data_row)
                if result:
                    self.all_data.append(result)
                
                # 在處理下一筆資料前稍作休息
                if i < len(self.input_data) - 1:
                    time.sleep(2)
            
            # 6. 儲存資料
            self.save_data()
            
            self.log(f"爬蟲執行完成！成功處理 {len(self.all_data)} 筆資料")
            return True
            
        except Exception as e:
            self.log(f"爬蟲執行失敗: {str(e)}")
            return False
        
        finally:
            # 關閉瀏覽器
            if hasattr(self, 'driver'):
                self.driver.quit()
                self.log("瀏覽器已關閉")

if __name__ == "__main__":
    print("=" * 60)
    print("Substrate Dashboard 專用爬蟲")
    print("處理input_data.csv中的資料並抓取所有Station統計")
    print("=" * 60)
    
    scraper = SubstrateDashboardScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ Dashboard爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.dashboard_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ Dashboard爬蟲執行失敗")
    
    print("\n📊 輸出檔案:")
    print("- dashboard_data_*.json (完整JSON資料)")
    print("- dashboard_data_*.csv (CSV格式資料)")
    print("- summary_*.json (處理摘要)")
