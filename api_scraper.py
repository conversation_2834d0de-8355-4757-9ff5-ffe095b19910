#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API爬蟲 - 嘗試抓取API端點資料
針對 10.22.22.144:31539 的API端點
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import time
import os
from datetime import datetime

class APIScraper:
    def __init__(self):
        """初始化API爬蟲"""
        self.base_url = 'http://10.22.22.144:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        self.session_cookies = {}
        self.scraped_data = []
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.data_dir = os.path.join(self.output_dir, 'api_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.data_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"api_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def make_request(self, url, data=None, method='GET', headers=None):
        """發送HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                if isinstance(data, dict):
                    data = json.dumps(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/json')
            else:
                req = urllib.request.Request(url)
            
            # 添加標頭
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.add_header('Accept', 'application/json, text/plain, */*')
            
            # 添加自定義標頭
            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            content = response.read().decode('utf-8')
            return content, response.getcode(), dict(response.headers)
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None, None
    
    def login(self):
        """登入系統"""
        self.log("開始API登入...")
        
        # 嘗試不同的登入端點
        login_endpoints = [
            '/api/login',
            '/api/auth/login',
            '/login',
            '/auth/login',
            '/api/v1/login',
            '/api/v1/auth/login'
        ]
        
        login_data = {
            'username': self.username,
            'password': self.password
        }
        
        for endpoint in login_endpoints:
            login_url = f"{self.base_url}{endpoint}"
            self.log(f"嘗試登入端點: {login_url}")
            
            content, status, headers = self.make_request(login_url, login_data, 'POST')
            
            if status == 200:
                self.log(f"登入端點 {endpoint} 回應成功")
                try:
                    if content:
                        json_data = json.loads(content)
                        self.log(f"登入回應: {json_data}")
                        if 'token' in json_data or 'access_token' in json_data:
                            self.log("發現認證令牌，登入成功！")
                            return True
                except:
                    pass
            elif status:
                self.log(f"登入端點 {endpoint} 回應狀態碼: {status}")
        
        self.log("所有登入端點都嘗試過了，繼續嘗試抓取資料...")
        return True  # 即使登入失敗也繼續
    
    def discover_api_endpoints(self):
        """發現API端點"""
        self.log("開始發現API端點...")
        
        # 常見的API端點
        api_endpoints = [
            '/api',
            '/api/v1',
            '/api/v2',
            '/api/status',
            '/api/health',
            '/api/info',
            '/api/data',
            '/api/substrate',
            '/api/substrate/overall',
            '/api/dashboard',
            '/api/metrics',
            '/api/stats',
            '/health',
            '/status',
            '/info',
            '/metrics',
            '/version',
            '/ping'
        ]
        
        discovered_endpoints = []
        
        for endpoint in api_endpoints:
            url = f"{self.base_url}{endpoint}"
            self.log(f"檢查端點: {url}")
            
            content, status, headers = self.make_request(url)
            
            if status == 200:
                self.log(f"發現有效端點: {endpoint}")
                discovered_endpoints.append({
                    'endpoint': endpoint,
                    'url': url,
                    'status': status,
                    'content': content,
                    'headers': headers,
                    'timestamp': datetime.now().isoformat()
                })
            elif status:
                self.log(f"端點 {endpoint} 回應狀態碼: {status}")
            
            time.sleep(0.5)  # 短暫延遲
        
        self.scraped_data.extend(discovered_endpoints)
        self.log(f"發現 {len(discovered_endpoints)} 個有效端點")
        return discovered_endpoints
    
    def scrape_specific_endpoints(self):
        """抓取特定端點的詳細資料"""
        self.log("開始抓取特定端點...")
        
        # 基於網站結構的特定端點
        specific_endpoints = [
            '/js/main.e7ca49.js',  # 從HTML中發現的JS檔案
            '/favicon.ico',
            '/api/substrate/overall',
            '/api/data',
            '/api/dashboard',
            '/substrate/overall',
            '/data',
            '/dashboard'
        ]
        
        for endpoint in specific_endpoints:
            url = f"{self.base_url}{endpoint}"
            self.log(f"抓取端點: {url}")
            
            content, status, headers = self.make_request(url)
            
            if status == 200:
                endpoint_data = {
                    'endpoint': endpoint,
                    'url': url,
                    'status': status,
                    'content': content,
                    'content_length': len(content) if content else 0,
                    'headers': headers,
                    'timestamp': datetime.now().isoformat(),
                    'content_type': headers.get('Content-Type', '') if headers else ''
                }
                
                # 嘗試解析JSON
                if content and 'application/json' in endpoint_data['content_type']:
                    try:
                        endpoint_data['json_data'] = json.loads(content)
                        self.log(f"成功解析JSON資料: {endpoint}")
                    except:
                        self.log(f"無法解析JSON資料: {endpoint}")
                
                self.scraped_data.append(endpoint_data)
                self.log(f"成功抓取端點: {endpoint}")
            else:
                self.log(f"端點 {endpoint} 無法訪問，狀態碼: {status}")
            
            time.sleep(0.5)
    
    def save_data(self):
        """儲存資料"""
        if not self.scraped_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整JSON資料
        json_file = os.path.join(self.data_dir, f"api_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        self.log(f"API資料已儲存: {json_file}")
        
        # 儲存摘要資料
        summary_data = []
        for item in self.scraped_data:
            summary = {
                'endpoint': item['endpoint'],
                'url': item['url'],
                'status': item['status'],
                'content_length': item.get('content_length', 0),
                'content_type': item.get('content_type', ''),
                'timestamp': item['timestamp'],
                'has_json': 'json_data' in item
            }
            summary_data.append(summary)
        
        summary_file = os.path.join(self.data_dir, f"api_summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        self.log(f"API摘要已儲存: {summary_file}")
        
        # 生成報告
        report_file = os.path.join(self.data_dir, f"api_report_{timestamp}.html")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_html_report())
        self.log(f"API報告已儲存: {report_file}")
    
    def generate_html_report(self):
        """生成HTML報告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>API爬蟲報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .endpoint {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .endpoint-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                .json-data {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; }}
                .status-200 {{ color: green; }}
                .status-error {{ color: red; }}
                pre {{ white-space: pre-wrap; word-wrap: break-word; }}
            </style>
        </head>
        <body>
            <h1>API爬蟲報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>共發現 {len(self.scraped_data)} 個端點</p>
        """
        
        for item in self.scraped_data:
            status_class = 'status-200' if item['status'] == 200 else 'status-error'
            
            html += f"""
            <div class="endpoint">
                <h2 class="endpoint-title">{item['endpoint']}</h2>
                <p><strong>URL:</strong> {item['url']}</p>
                <p><strong>狀態碼:</strong> <span class="{status_class}">{item['status']}</span></p>
                <p><strong>內容類型:</strong> {item.get('content_type', 'N/A')}</p>
                <p><strong>內容長度:</strong> {item.get('content_length', 0)} 字元</p>
                <p><strong>抓取時間:</strong> {item['timestamp']}</p>
            """
            
            if 'json_data' in item:
                html += f"""
                <h3>JSON資料</h3>
                <div class="json-data">
                    <pre>{json.dumps(item['json_data'], ensure_ascii=False, indent=2)}</pre>
                </div>
                """
            elif item.get('content') and len(item['content']) < 1000:
                html += f"""
                <h3>內容預覽</h3>
                <div class="json-data">
                    <pre>{item['content'][:500]}...</pre>
                </div>
                """
            
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        return html
    
    def run(self):
        """執行API爬蟲"""
        self.log("開始執行API爬蟲...")
        
        try:
            # 登入
            self.login()
            
            # 發現API端點
            self.discover_api_endpoints()
            
            # 抓取特定端點
            self.scrape_specific_endpoints()
            
            # 儲存資料
            self.save_data()
            
            self.log("API爬蟲執行完成！")
            return True
            
        except Exception as e:
            self.log(f"API爬蟲執行過程中發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 50)
    print("API爬蟲 - 10.22.22.144:31539")
    print("=" * 50)
    
    scraper = APIScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ API爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.data_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ API爬蟲執行失敗")
