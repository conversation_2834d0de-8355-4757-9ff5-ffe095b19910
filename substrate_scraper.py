#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Substrate Overall 頁面專用爬蟲
抓取 http://************:31539/substrate/overall 的互動式資料
支援多種篩選條件和Station選擇
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import csv
import time
import os
import re
from datetime import datetime, timedelta
import html.parser

class SubstrateHTMLParser(html.parser.HTMLParser):
    """專門解析Substrate頁面的HTML解析器"""
    def __init__(self):
        super().__init__()
        self.forms = []
        self.current_form = None
        self.inputs = []
        self.selects = []
        self.current_select = None
        self.options = []
        self.tables = []
        self.current_table = None
        self.current_row = None
        self.current_cell = ""
        self.in_table = False
        self.in_row = False
        self.in_cell = False
        self.in_select = False
        self.in_option = False
        
    def handle_starttag(self, tag, attrs):
        attrs_dict = dict(attrs)
        
        if tag == 'form':
            self.current_form = {
                'action': attrs_dict.get('action', ''),
                'method': attrs_dict.get('method', 'GET'),
                'inputs': [],
                'selects': []
            }
        elif tag == 'input':
            input_data = {
                'name': attrs_dict.get('name', ''),
                'type': attrs_dict.get('type', 'text'),
                'value': attrs_dict.get('value', ''),
                'id': attrs_dict.get('id', ''),
                'class': attrs_dict.get('class', '')
            }
            self.inputs.append(input_data)
            if self.current_form:
                self.current_form['inputs'].append(input_data)
        elif tag == 'select':
            self.in_select = True
            self.current_select = {
                'name': attrs_dict.get('name', ''),
                'id': attrs_dict.get('id', ''),
                'class': attrs_dict.get('class', ''),
                'options': []
            }
        elif tag == 'option' and self.in_select:
            self.in_option = True
            option_data = {
                'value': attrs_dict.get('value', ''),
                'text': ''
            }
            self.options.append(option_data)
            if self.current_select:
                self.current_select['options'].append(option_data)
        elif tag == 'table':
            self.in_table = True
            self.current_table = {
                'class': attrs_dict.get('class', ''),
                'id': attrs_dict.get('id', ''),
                'rows': []
            }
        elif tag == 'tr' and self.in_table:
            self.in_row = True
            self.current_row = []
        elif tag in ['td', 'th'] and self.in_row:
            self.in_cell = True
            self.current_cell = ""
            
    def handle_endtag(self, tag):
        if tag == 'form' and self.current_form:
            self.forms.append(self.current_form)
            self.current_form = None
        elif tag == 'select':
            self.in_select = False
            if self.current_select:
                self.selects.append(self.current_select)
                if self.current_form:
                    self.current_form['selects'].append(self.current_select)
            self.current_select = None
        elif tag == 'option':
            self.in_option = False
        elif tag == 'table':
            self.in_table = False
            if self.current_table:
                self.tables.append(self.current_table)
            self.current_table = None
        elif tag == 'tr' and self.in_table:
            self.in_row = False
            if self.current_row and self.current_table:
                self.current_table['rows'].append(self.current_row)
            self.current_row = None
        elif tag in ['td', 'th'] and self.in_cell:
            self.in_cell = False
            if self.current_row is not None:
                self.current_row.append(self.current_cell.strip())
            self.current_cell = ""
            
    def handle_data(self, data):
        if self.in_cell:
            self.current_cell += data
        elif self.in_option and self.options:
            self.options[-1]['text'] = data.strip()

class SubstrateScraper:
    def __init__(self):
        """初始化Substrate爬蟲"""
        self.base_url = 'http://************:31539'
        self.target_url = 'http://************:31539/substrate/overall'
        self.username = 'admin'
        self.password = 'pega#1234'
        self.session_cookies = {}
        self.scraped_data = []
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.data_dir = os.path.join(self.output_dir, 'substrate_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.data_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"substrate_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def make_request(self, url, data=None, method='GET', headers=None):
        """發送HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                if isinstance(data, dict):
                    data = urllib.parse.urlencode(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/x-www-form-urlencoded')
            else:
                req = urllib.request.Request(url)
            
            # 添加標頭
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.add_header('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8')
            req.add_header('Accept-Language', 'zh-TW,zh;q=0.9,en;q=0.8')
            
            # 添加自定義標頭
            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            content = response.read().decode('utf-8')
            return content, response.getcode(), dict(response.headers)
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None, None
    
    def login(self):
        """登入系統"""
        self.log("開始登入...")
        
        # 首先訪問登入頁面
        login_url = f"{self.base_url}/login"
        content, status, headers = self.make_request(login_url)
        
        if status == 200:
            self.log("成功訪問登入頁面")
            
            # 準備登入資料
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 發送登入請求
            content, status, headers = self.make_request(login_url, login_data, 'POST')
            
            if status == 200:
                self.log("登入請求已發送")
                return True
            else:
                self.log(f"登入失敗，狀態碼: {status}")
                return False
        else:
            self.log(f"無法訪問登入頁面，狀態碼: {status}")
            return False
    
    def get_page_structure(self):
        """分析頁面結構"""
        self.log("分析頁面結構...")
        
        content, status, headers = self.make_request(self.target_url)
        
        if status == 200 and content:
            parser = SubstrateHTMLParser()
            parser.feed(content)
            
            page_structure = {
                'forms': parser.forms,
                'inputs': parser.inputs,
                'selects': parser.selects,
                'tables': parser.tables,
                'raw_html': content
            }
            
            self.log(f"發現 {len(parser.forms)} 個表單")
            self.log(f"發現 {len(parser.inputs)} 個輸入欄位")
            self.log(f"發現 {len(parser.selects)} 個選擇欄位")
            self.log(f"發現 {len(parser.tables)} 個表格")
            
            return page_structure
        else:
            self.log(f"無法獲取頁面結構，狀態碼: {status}")
            return None
    
    def scrape_with_filters(self, station=None, start_date=None, end_date=None, additional_filters=None):
        """使用篩選條件抓取資料"""
        self.log(f"開始抓取資料 - Station: {station}, 日期範圍: {start_date} ~ {end_date}")
        
        # 準備篩選參數
        params = {}
        
        if station:
            params['station'] = station
        
        if start_date:
            params['start_date'] = start_date
            
        if end_date:
            params['end_date'] = end_date
            
        if additional_filters:
            params.update(additional_filters)
        
        # 構建URL
        if params:
            query_string = urllib.parse.urlencode(params)
            url = f"{self.target_url}?{query_string}"
        else:
            url = self.target_url
        
        # 發送請求
        content, status, headers = self.make_request(url)
        
        if status == 200 and content:
            parser = SubstrateHTMLParser()
            parser.feed(content)
            
            scrape_result = {
                'station': station,
                'start_date': start_date,
                'end_date': end_date,
                'filters': params,
                'timestamp': datetime.now().isoformat(),
                'url': url,
                'status_code': status,
                'tables': parser.tables,
                'forms': parser.forms,
                'raw_html': content
            }
            
            self.scraped_data.append(scrape_result)
            self.log(f"成功抓取資料 - Station: {station}")
            return scrape_result
        else:
            self.log(f"抓取失敗 - Station: {station}, 狀態碼: {status}")
            return None
    
    def scrape_all_stations(self):
        """抓取所有Station的資料"""
        self.log("開始抓取所有Station的資料...")
        
        # 設定日期範圍（最近7天）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # 首先抓取無篩選的資料
        self.scrape_with_filters()
        time.sleep(2)
        
        # 針對每個Station抓取資料
        for station in self.stations:
            self.scrape_with_filters(
                station=station,
                start_date=start_date_str,
                end_date=end_date_str
            )
            time.sleep(2)  # 延遲避免過於頻繁的請求
        
        # 嘗試不同的篩選組合
        additional_filters_list = [
            {'status': 'OK'},
            {'status': 'NG'},
            {'type': 'AI_Loss'},
            {'type': 'AI_Overkill'}
        ]
        
        for filters in additional_filters_list:
            for station in self.stations:
                self.scrape_with_filters(
                    station=station,
                    start_date=start_date_str,
                    end_date=end_date_str,
                    additional_filters=filters
                )
                time.sleep(1)
        
        self.log(f"完成所有Station資料抓取，共 {len(self.scraped_data)} 筆資料")
    
    def save_data(self):
        """儲存資料"""
        if not self.scraped_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整JSON資料
        json_file = os.path.join(self.data_dir, f"substrate_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        self.log(f"完整資料已儲存: {json_file}")
        
        # 儲存表格資料為CSV
        self.save_tables_as_csv(timestamp)
        
        # 儲存摘要資料
        self.save_summary_data(timestamp)
        
        # 生成HTML報告
        report_file = os.path.join(self.data_dir, f"substrate_report_{timestamp}.html")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_html_report())
        self.log(f"HTML報告已儲存: {report_file}")
    
    def save_tables_as_csv(self, timestamp):
        """將表格資料儲存為CSV"""
        for i, data in enumerate(self.scraped_data):
            if data['tables']:
                for j, table in enumerate(data['tables']):
                    if table['rows']:
                        csv_file = os.path.join(
                            self.data_dir, 
                            f"table_{data['station'] or 'all'}_{i}_{j}_{timestamp}.csv"
                        )
                        
                        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                            writer = csv.writer(f)
                            for row in table['rows']:
                                writer.writerow(row)
                        
                        self.log(f"表格資料已儲存: {csv_file}")
    
    def save_summary_data(self, timestamp):
        """儲存摘要資料"""
        summary_data = []
        
        for data in self.scraped_data:
            summary = {
                'station': data['station'],
                'start_date': data['start_date'],
                'end_date': data['end_date'],
                'timestamp': data['timestamp'],
                'status_code': data['status_code'],
                'tables_count': len(data['tables']),
                'total_rows': sum(len(table['rows']) for table in data['tables']),
                'filters': data['filters']
            }
            summary_data.append(summary)
        
        summary_file = os.path.join(self.data_dir, f"substrate_summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        self.log(f"摘要資料已儲存: {summary_file}")
    
    def generate_html_report(self):
        """生成HTML報告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Substrate Overall 資料報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .data-section {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .section-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .station {{ font-weight: bold; color: #007bff; }}
                .timestamp {{ color: #666; font-size: 0.9em; }}
            </style>
        </head>
        <body>
            <h1>Substrate Overall 資料報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>共抓取 {len(self.scraped_data)} 筆資料</p>
        """
        
        for data in self.scraped_data:
            html += f"""
            <div class="data-section">
                <h2 class="section-title">Station: <span class="station">{data['station'] or 'All'}</span></h2>
                <p class="timestamp">抓取時間: {data['timestamp']}</p>
                <p>日期範圍: {data['start_date']} ~ {data['end_date']}</p>
                <p>篩選條件: {data['filters']}</p>
                <p>狀態碼: {data['status_code']}</p>
                <p>表格數量: {len(data['tables'])}</p>
            """
            
            for i, table in enumerate(data['tables']):
                if table['rows']:
                    html += f"<h3>表格 {i+1}</h3><table>"
                    for row in table['rows'][:20]:  # 只顯示前20行
                        html += "<tr>"
                        for cell in row:
                            html += f"<td>{cell}</td>"
                        html += "</tr>"
                    html += "</table>"
            
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        return html
    
    def run(self):
        """執行Substrate爬蟲"""
        self.log("開始執行Substrate爬蟲...")
        
        try:
            # 登入
            if self.login():
                # 分析頁面結構
                structure = self.get_page_structure()
                if structure:
                    self.log("頁面結構分析完成")
                
                # 抓取所有Station的資料
                self.scrape_all_stations()
                
                # 儲存資料
                self.save_data()
                
                self.log("Substrate爬蟲執行完成！")
                return True
            else:
                self.log("登入失敗，但嘗試繼續抓取...")
                # 即使登入失敗也嘗試抓取
                structure = self.get_page_structure()
                self.scrape_all_stations()
                self.save_data()
                return True
                
        except Exception as e:
            self.log(f"Substrate爬蟲執行過程中發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("Substrate Overall 專用爬蟲")
    print("目標: http://************:31539/substrate/overall")
    print("=" * 60)
    
    scraper = SubstrateScraper()
    success = scraper.run()
    
    if success:
        print("\n✅ Substrate爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.data_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(scraper.log_dir)}")
    else:
        print("\n❌ Substrate爬蟲執行失敗")
