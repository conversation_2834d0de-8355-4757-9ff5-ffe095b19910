#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復 lxml.html.clean 問題的解決方案
"""

import subprocess
import sys
import os

def install_package(package):
    """安裝套件"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安裝 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安裝 {package} 失敗: {e}")
        return False

def fix_lxml_issue():
    """修復 lxml 問題"""
    print("=" * 60)
    print("修復 lxml.html.clean 問題")
    print("=" * 60)
    
    print("問題：lxml.html.clean 模組已經分離為獨立專案")
    print("解決方案：安裝 lxml_html_clean")
    print()
    
    # 方案1: 安裝 lxml[html_clean]
    print("方案1: 安裝 lxml[html_clean]...")
    success1 = install_package("lxml[html_clean]")
    
    if not success1:
        # 方案2: 直接安裝 lxml_html_clean
        print("\n方案2: 直接安裝 lxml_html_clean...")
        success2 = install_package("lxml_html_clean")
        
        if not success2:
            # 方案3: 升級 lxml 並安裝 lxml_html_clean
            print("\n方案3: 升級 lxml 並安裝 lxml_html_clean...")
            install_package("--upgrade lxml")
            success3 = install_package("lxml_html_clean")
            
            if not success3:
                print("\n❌ 所有安裝方案都失敗")
                return False
    
    print("\n✅ lxml 問題已修復")
    return True

def test_lxml():
    """測試 lxml 是否正常工作"""
    print("\n" + "=" * 60)
    print("測試 lxml 功能")
    print("=" * 60)
    
    try:
        import lxml
        print(f"✅ lxml 版本: {lxml.__version__}")
        
        from lxml import html
        print("✅ lxml.html 正常")
        
        # 測試基本功能
        doc = html.fromstring("<html><body><p>Test</p></body></html>")
        print("✅ lxml.html.fromstring 正常")
        
        # 測試是否需要 clean 功能
        try:
            from lxml.html.clean import Cleaner
            print("✅ lxml.html.clean 正常")
        except ImportError:
            try:
                from lxml_html_clean import Cleaner
                print("✅ lxml_html_clean 正常")
            except ImportError:
                print("⚠️  lxml.html.clean 不可用，但基本功能正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ lxml 測試失敗: {e}")
        return False

def create_alternative_scraper():
    """建立不依賴 lxml.html.clean 的替代爬蟲"""
    print("\n" + "=" * 60)
    print("建立替代爬蟲 (不依賴 lxml.html.clean)")
    print("=" * 60)
    
    alternative_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替代版本的 Substrate Dashboard 爬蟲
不依賴 lxml.html.clean，使用 BeautifulSoup 替代
"""

import pandas as pd
import json
import time
import os
import re
from datetime import datetime
import urllib.request
import urllib.parse
from bs4 import BeautifulSoup

class AlternativeSubstrateScraper:
    def __init__(self):
        """初始化替代爬蟲"""
        self.base_url = 'http://10.22.22.144:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.alternative_dir = os.path.join(self.output_dir, 'alternative_data')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.alternative_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"alternative_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
        # 儲存所有抓取的資料
        self.all_data = []
        self.input_data = []
        self.session_cookies = {}
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\\n')
    
    def load_input_data(self, csv_file='input_data.csv'):
        """載入輸入資料"""
        try:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                self.input_data = df.to_dict('records')
                self.log(f"成功載入 {len(self.input_data)} 筆輸入資料")
                return True
            else:
                self.log(f"找不到檔案: {csv_file}")
                return False
        except Exception as e:
            self.log(f"載入輸入資料失敗: {str(e)}")
            return False
    
    def make_request(self, url, data=None, method='GET'):
        """發送HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                if isinstance(data, dict):
                    data = urllib.parse.urlencode(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/x-www-form-urlencoded')
            else:
                req = urllib.request.Request(url)
            
            # 添加標頭
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            for key, value in headers.items():
                req.add_header(key, value)
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            content = response.read()
            
            # 處理gzip編碼
            if response.headers.get('Content-Encoding') == 'gzip':
                import gzip
                content = gzip.decompress(content)
            
            content = content.decode('utf-8')
            return content, response.getcode()
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None
    
    def login(self):
        """登入系統"""
        try:
            self.log("開始登入...")
            
            # 訪問主頁面
            content, status = self.make_request(self.base_url)
            
            if status == 200:
                self.log("成功訪問主頁面")
                
                # 嘗試登入
                login_data = {
                    'username': self.username,
                    'password': self.password
                }
                
                content, status = self.make_request(f"{self.base_url}/login", login_data, 'POST')
                
                if status == 200:
                    self.log("登入請求成功")
                    return True
                else:
                    self.log(f"登入失敗，狀態碼: {status}")
                    return False
            else:
                self.log(f"無法訪問主頁面，狀態碼: {status}")
                return False
                
        except Exception as e:
            self.log(f"登入失敗: {str(e)}")
            return False
    
    def scrape_substrate_page(self, input_row):
        """抓取substrate頁面資料"""
        try:
            self.log(f"抓取substrate頁面資料: {input_row}")
            
            # 訪問substrate頁面
            url = f"{self.base_url}/substrate/overall"
            content, status = self.make_request(url)
            
            if status != 200:
                self.log(f"無法訪問substrate頁面，狀態碼: {status}")
                return None
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 分析頁面內容
            result = {
                'input_data': input_row,
                'timestamp': datetime.now().isoformat(),
                'image_counts': {},
                'station_details': {},
                'page_title': soup.title.string if soup.title else 'Unknown'
            }
            
            # 抓取圖片計數
            self.extract_image_counts_from_soup(soup, result)
            
            # 抓取Station詳細資料
            self.extract_station_details_from_soup(soup, result)
            
            return result
            
        except Exception as e:
            self.log(f"抓取substrate頁面失敗: {str(e)}")
            return None
    
    def extract_image_counts_from_soup(self, soup, result):
        """從BeautifulSoup中抓取圖片計數"""
        try:
            text_content = soup.get_text()
            
            # 使用正則表達式尋找數字模式
            numbers = re.findall(r'\\d+', text_content)
            
            # 尋找特定的計數標籤
            if 'OK Image Count' in text_content:
                ok_match = re.search(r'OK Image Count[:\\s]*(\\d+)', text_content)
                if ok_match:
                    result['image_counts']['OK_Image_Count'] = ok_match.group(1)
            
            if 'NG Image Count' in text_content:
                ng_match = re.search(r'NG Image Count[:\\s]*(\\d+)', text_content)
                if ng_match:
                    result['image_counts']['NG_Image_Count'] = ng_match.group(1)
            
            if 'Uncertain Image Count' in text_content:
                uncertain_match = re.search(r'Uncertain Image Count[:\\s]*(\\d+)', text_content)
                if uncertain_match:
                    result['image_counts']['Uncertain_Image_Count'] = uncertain_match.group(1)
            
            # 如果沒有找到特定標籤，嘗試從頁面結構中推斷
            if not result['image_counts']:
                large_numbers = [n for n in numbers if len(n) >= 3]
                if len(large_numbers) >= 3:
                    result['image_counts'] = {
                        'OK_Image_Count': large_numbers[0] if len(large_numbers) > 0 else '0',
                        'NG_Image_Count': large_numbers[1] if len(large_numbers) > 1 else '0',
                        'Uncertain_Image_Count': large_numbers[2] if len(large_numbers) > 2 else '0'
                    }
            
            self.log(f"抓取到圖片計數: {result['image_counts']}")
            
        except Exception as e:
            self.log(f"抓取圖片計數失敗: {str(e)}")
    
    def extract_station_details_from_soup(self, soup, result):
        """從BeautifulSoup中抓取Station詳細資料"""
        try:
            text_content = soup.get_text()
            
            for station in self.stations:
                result['station_details'][station] = {
                    'OK': [],
                    'NG': [],
                    'SKIP': []
                }
                
                # 尋找包含Station名稱的區域
                if station in text_content:
                    # 使用正則表達式尋找該Station附近的數字
                    station_pattern = rf'{station}[^\\d]*(\\d+)[^\\d]*(\\d+)[^\\d]*(\\d+)'
                    station_match = re.search(station_pattern, text_content)
                    
                    if station_match:
                        result['station_details'][station] = {
                            'OK': [station_match.group(1)],
                            'NG': [station_match.group(2)],
                            'SKIP': [station_match.group(3)]
                        }
                        self.log(f"找到 {station} 的數據: OK={station_match.group(1)}, NG={station_match.group(2)}, SKIP={station_match.group(3)}")
            
        except Exception as e:
            self.log(f"抓取Station詳細資料失敗: {str(e)}")
    
    def save_data(self):
        """儲存所有資料"""
        if not self.all_data:
            self.log("沒有資料可儲存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整JSON資料
        json_file = os.path.join(self.alternative_dir, f"alternative_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.all_data, f, ensure_ascii=False, indent=2)
        self.log(f"完整資料已儲存: {json_file}")
        
        # 轉換為CSV格式
        csv_data = []
        for record in self.all_data:
            base_row = {
                'input_code': record['input_data'].get('code', ''),
                'input_lot': record['input_data'].get('lot', ''),
                'input_ship': record['input_data'].get('ship', ''),
                'timestamp': record['timestamp'],
                'ok_image_count': record['image_counts'].get('OK_Image_Count', ''),
                'ng_image_count': record['image_counts'].get('NG_Image_Count', ''),
                'uncertain_image_count': record['image_counts'].get('Uncertain_Image_Count', '')
            }
            
            # 添加每個Station的詳細數據
            for station, details in record['station_details'].items():
                station_row = base_row.copy()
                station_row['station'] = station
                station_row['station_ok'] = ','.join(details.get('OK', []))
                station_row['station_ng'] = ','.join(details.get('NG', []))
                station_row['station_skip'] = ','.join(details.get('SKIP', []))
                csv_data.append(station_row)
        
        # 儲存CSV檔案
        csv_file = os.path.join(self.alternative_dir, f"alternative_data_{timestamp}.csv")
        if csv_data:
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False)
            self.log(f"CSV資料已儲存: {csv_file}")
    
    def run(self):
        """執行完整的爬蟲流程"""
        try:
            self.log("開始執行替代版爬蟲...")
            
            # 1. 載入輸入資料
            if not self.load_input_data():
                return False
            
            # 2. 登入
            if not self.login():
                self.log("登入失敗，但繼續嘗試抓取...")
            
            # 3. 處理每一筆輸入資料
            for i, data_row in enumerate(self.input_data):
                self.log(f"處理第 {i+1}/{len(self.input_data)} 筆資料")
                
                result = self.scrape_substrate_page(data_row)
                if result:
                    self.all_data.append(result)
                
                # 在處理下一筆資料前稍作休息
                if i < len(self.input_data) - 1:
                    time.sleep(3)
            
            # 4. 儲存資料
            self.save_data()
            
            self.log(f"爬蟲執行完成！成功處理 {len(self.all_data)} 筆資料")
            return True
            
        except Exception as e:
            self.log(f"爬蟲執行失敗: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("替代版 Substrate Dashboard 爬蟲")
    print("使用 BeautifulSoup，不依賴 lxml.html.clean")
    print("=" * 60)
    
    scraper = AlternativeSubstrateScraper()
    success = scraper.run()
    
    if success:
        print("\\n✅ 替代版爬蟲執行成功！")
        print(f"📁 資料儲存在: {os.path.abspath(scraper.alternative_dir)}")
    else:
        print("\\n❌ 替代版爬蟲執行失敗")
'''
    
    # 儲存替代爬蟲
    with open('alternative_scraper.py', 'w', encoding='utf-8') as f:
        f.write(alternative_code)
    
    print("✅ 已建立 alternative_scraper.py")
    print("這個版本使用 BeautifulSoup 而不是 requests-html")
    print("執行: python alternative_scraper.py")

def main():
    """主要修復流程"""
    print("lxml.html.clean 問題修復工具")
    print("=" * 60)
    
    # 1. 修復 lxml 問題
    if fix_lxml_issue():
        # 2. 測試 lxml
        if test_lxml():
            print("\n🎉 lxml 問題已完全修復！")
            print("您現在可以正常使用 requests-html")
            print("執行: python requests_html_scraper.py")
        else:
            print("\n⚠️  lxml 基本功能正常，但可能缺少某些功能")
            create_alternative_scraper()
    else:
        print("\n❌ 無法修復 lxml 問題")
        create_alternative_scraper()
    
    print("\n" + "=" * 60)
    print("修復完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
