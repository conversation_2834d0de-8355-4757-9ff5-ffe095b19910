# PowerShell script to run the web scraper
Write-Host "========================================" -ForegroundColor Green
Write-Host "Web Scraper for ************:31539" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "虚拟环境不存在，正在创建..." -ForegroundColor Yellow
    
    # Try different Python commands
    $pythonCommands = @("python", "python3", "py")
    $pythonCommand = $null
    
    foreach ($cmd in $pythonCommands) {
        try {
            $version = & $cmd --version 2>&1
            if ($version -match "Python") {
                $pythonCommand = $cmd
                Write-Host "找到Python: $version" -ForegroundColor Green
                break
            }
        } catch {
            # Command not found, continue to next
        }
    }
    
    if ($null -eq $pythonCommand) {
        Write-Host "Python未找到，请安装Python后重试" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    
    # Create virtual environment
    & $pythonCommand -m venv venv
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "创建虚拟环境失败" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    
    Write-Host "虚拟环境创建成功！" -ForegroundColor Green
    Write-Host ""
}

# Activate virtual environment
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Check if activation was successful
Write-Host "当前Python路径:" -ForegroundColor Cyan
Get-Command python | Select-Object Source
Write-Host ""

# Install dependencies
Write-Host "安装依赖包..." -ForegroundColor Yellow
pip install -r requirements.txt
Write-Host ""

# Check installed packages
Write-Host "检查已安装的包:" -ForegroundColor Cyan
pip list
Write-Host ""

# Run the scraper
Write-Host "开始运行爬虫..." -ForegroundColor Green
Write-Host ""
python run_scraper.py

# Keep window open
Write-Host ""
Read-Host "按回车键退出"
