
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>API探索報告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .api-section { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
                .section-title { color: #333; border-bottom: 2px solid #007bff; }
                .json-data { background-color: #f8f9fa; padding: 10px; border-radius: 5px; }
                .success { color: green; }
                .endpoint { font-weight: bold; color: #007bff; }
                pre { white-space: pre-wrap; word-wrap: break-word; }
            </style>
        </head>
        <body>
            <h1>API探索報告</h1>
            <p>生成時間: 2025-06-16 17:56:35</p>
            <p>共發現 25 個有效API端點</p>
        
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:51.033185</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:51.577955</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/summary</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/summary</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:52.157910</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/stations</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/stations</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:52.783894</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/statistics</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/statistics</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:53.371390</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:53.957287</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:54.588014</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/stations</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/stations</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:55.195983</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/dashboard/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/dashboard/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:55.776808</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/dashboard/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/dashboard/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:56.352421</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/statistics</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/statistics</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:56.918956</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/reports</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/reports</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:57.520335</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/export</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/export</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:58.094033</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:58.680579</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:59.297430</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/summary</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/summary</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:55:59.901328</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/data/api/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/data/api/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:00.501482</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/data/api/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/data/api/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:01.101431</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/backend/api/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/backend/api/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:01.702428</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/backend/api/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/backend/api/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:02.291508</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:02.875913</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:03.455869</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/data/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/data/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:04.033029</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v2/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v2/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:04.623586</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v2/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v2/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:56:05.245756</p>
            </div>
        </body>
        </html>
        