
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>API探索報告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .api-section { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
                .section-title { color: #333; border-bottom: 2px solid #007bff; }
                .json-data { background-color: #f8f9fa; padding: 10px; border-radius: 5px; }
                .success { color: green; }
                .endpoint { font-weight: bold; color: #007bff; }
                pre { white-space: pre-wrap; word-wrap: break-word; }
            </style>
        </head>
        <body>
            <h1>API探索報告</h1>
            <p>生成時間: 2025-06-16 17:55:26</p>
            <p>共發現 25 個有效API端點</p>
        
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:37.931125</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:38.577935</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/summary</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/summary</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:39.193820</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/stations</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/stations</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:39.808548</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/substrate/statistics</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/substrate/statistics</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:40.415353</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:41.067432</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:41.672770</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/data/stations</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/data/stations</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:42.279467</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/dashboard/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/dashboard/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:42.903310</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/dashboard/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/dashboard/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:43.527761</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/statistics</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/statistics</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:44.159705</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/reports</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/reports</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:44.825550</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/api/export</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/api/export</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:45.475843</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:46.157693</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:46.832771</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/substrate/api/summary</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/substrate/api/summary</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:47.474186</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/data/api/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/data/api/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:48.118000</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/data/api/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/data/api/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:48.789290</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/backend/api/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/backend/api/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:49.432455</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/backend/api/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/backend/api/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:50.045881</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:50.703067</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:51.356743</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v1/data/substrate</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v1/data/substrate</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:51.978008</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v2/substrate/overall</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v2/substrate/overall</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:52.605707</p>
            </div>
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">/v2/substrate/data</span></h2>
                <p><strong>URL:</strong> http://10.22.22.144:31539/v2/substrate/data</p>
                <p><strong>方法:</strong> GET</p>
                <p><strong>狀態碼:</strong> <span class="success">200</span></p>
                <p><strong>時間:</strong> 2025-06-16T17:54:53.213991</p>
            </div>
        </body>
        </html>
        