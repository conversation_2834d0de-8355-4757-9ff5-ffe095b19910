#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API探索爬蟲 - 專門用於發現和抓取SPA應用的API端點
針對 PEGA AI 系統的API探索
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import time
import os
import re
from datetime import datetime, timedelta

class APIExplorer:
    def __init__(self):
        """初始化API探索器"""
        self.base_url = 'http://10.22.22.144:31539'
        self.username = 'admin'
        self.password = 'pega#1234'
        self.session_cookies = {}
        self.discovered_apis = []
        
        # 建立輸出目錄
        self.output_dir = 'scraped_data_20250616'
        self.data_dir = os.path.join(self.output_dir, 'api_exploration')
        self.log_dir = os.path.join(self.output_dir, 'logs')
        
        for dir_path in [self.data_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"建立目錄: {dir_path}")
        
        # 設定日誌檔案
        self.log_file = os.path.join(self.log_dir, f"api_explorer_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Station列表
        self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
        
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def make_request(self, url, data=None, method='GET', headers=None):
        """發送HTTP請求"""
        try:
            # 準備請求
            if data and method == 'POST':
                if isinstance(data, dict):
                    data = json.dumps(data).encode('utf-8')
                req = urllib.request.Request(url, data=data)
                req.add_header('Content-Type', 'application/json')
            else:
                req = urllib.request.Request(url)
            
            # 添加標頭
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.add_header('Accept', 'application/json, text/plain, */*')
            req.add_header('Accept-Language', 'zh-TW,zh;q=0.9,en;q=0.8')
            
            # 添加自定義標頭
            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)
            
            # 添加cookies
            if self.session_cookies:
                cookie_string = '; '.join([f"{k}={v}" for k, v in self.session_cookies.items()])
                req.add_header('Cookie', cookie_string)
            
            # 發送請求
            response = urllib.request.urlopen(req, timeout=30)
            
            # 處理cookies
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                for cookie in cookies.split(','):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        self.session_cookies[key.strip()] = value.split(';')[0].strip()
            
            content = response.read().decode('utf-8')
            return content, response.getcode(), dict(response.headers)
            
        except Exception as e:
            self.log(f"請求失敗: {url}, 錯誤: {str(e)}")
            return None, None, None
    
    def analyze_javascript(self):
        """分析JavaScript檔案以發現API端點"""
        self.log("分析JavaScript檔案...")
        
        js_url = f"{self.base_url}/js/main.e7ca49.js"
        content, status, headers = self.make_request(js_url)
        
        if status == 200 and content:
            self.log(f"成功獲取JavaScript檔案，大小: {len(content)} 字元")
            
            # 尋找API端點模式
            api_patterns = [
                r'/api/[a-zA-Z0-9/_-]+',
                r'"/[a-zA-Z0-9/_-]*api[a-zA-Z0-9/_-]*"',
                r"'/[a-zA-Z0-9/_-]*api[a-zA-Z0-9/_-]*'",
                r'/substrate/[a-zA-Z0-9/_-]+',
                r'/data[a-zA-Z0-9/_-]*',
                r'endpoint["\']?\s*:\s*["\'][^"\']+["\']',
                r'url["\']?\s*:\s*["\'][^"\']+["\']'
            ]
            
            discovered_endpoints = set()
            
            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    # 清理匹配結果
                    endpoint = match.strip('"\'')
                    if endpoint.startswith('/'):
                        discovered_endpoints.add(endpoint)
            
            self.log(f"從JavaScript中發現 {len(discovered_endpoints)} 個潛在端點")
            
            # 儲存JavaScript內容供分析
            js_file = os.path.join(self.data_dir, "main_js_content.txt")
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.log(f"JavaScript內容已儲存: {js_file}")
            
            return list(discovered_endpoints)
        else:
            self.log(f"無法獲取JavaScript檔案，狀態碼: {status}")
            return []
    
    def explore_common_api_endpoints(self):
        """探索常見的API端點"""
        self.log("探索常見API端點...")
        
        # 基於PEGA AI和substrate的常見端點
        common_endpoints = [
            '/api/substrate/overall',
            '/api/substrate/data',
            '/api/substrate/summary',
            '/api/substrate/stations',
            '/api/substrate/statistics',
            '/api/data/substrate',
            '/api/data/overall',
            '/api/data/stations',
            '/api/dashboard/data',
            '/api/dashboard/substrate',
            '/api/statistics',
            '/api/reports',
            '/api/export',
            '/substrate/api/overall',
            '/substrate/api/data',
            '/substrate/api/summary',
            '/data/api/substrate',
            '/data/api/overall',
            '/backend/api/substrate',
            '/backend/api/data',
            '/v1/substrate/overall',
            '/v1/substrate/data',
            '/v1/data/substrate',
            '/v2/substrate/overall',
            '/v2/substrate/data'
        ]
        
        successful_endpoints = []
        
        for endpoint in common_endpoints:
            url = f"{self.base_url}{endpoint}"
            self.log(f"測試端點: {endpoint}")
            
            content, status, headers = self.make_request(url)
            
            if status == 200:
                self.log(f"✅ 發現有效端點: {endpoint}")
                
                endpoint_data = {
                    'endpoint': endpoint,
                    'url': url,
                    'status': status,
                    'content': content,
                    'headers': headers,
                    'timestamp': datetime.now().isoformat(),
                    'content_type': headers.get('Content-Type', '') if headers else ''
                }
                
                # 嘗試解析JSON
                if content:
                    try:
                        json_data = json.loads(content)
                        endpoint_data['json_data'] = json_data
                        endpoint_data['is_json'] = True
                        self.log(f"✅ 端點返回JSON資料: {endpoint}")
                    except:
                        endpoint_data['is_json'] = False
                
                successful_endpoints.append(endpoint_data)
                self.discovered_apis.append(endpoint_data)
                
            elif status:
                self.log(f"❌ 端點 {endpoint} 回應狀態碼: {status}")
            
            time.sleep(0.5)  # 避免過於頻繁的請求
        
        return successful_endpoints
    
    def test_substrate_endpoints_with_params(self):
        """測試substrate端點的不同參數組合"""
        self.log("測試substrate端點的參數組合...")
        
        # 基礎端點
        base_endpoints = [
            '/api/substrate/overall',
            '/substrate/overall',
            '/api/data',
            '/data'
        ]
        
        # 參數組合
        param_combinations = [
            {},  # 無參數
            {'station': 'DF1'},
            {'station': 'DF2'},
            {'start_date': '2025-06-09', 'end_date': '2025-06-16'},
            {'station': 'DF1', 'start_date': '2025-06-09', 'end_date': '2025-06-16'},
            {'status': 'OK'},
            {'status': 'NG'},
            {'type': 'AI_Loss'},
            {'type': 'AI_Overkill'},
            {'limit': '100'},
            {'page': '1', 'limit': '50'}
        ]
        
        successful_requests = []
        
        for endpoint in base_endpoints:
            for params in param_combinations:
                if params:
                    query_string = urllib.parse.urlencode(params)
                    url = f"{self.base_url}{endpoint}?{query_string}"
                else:
                    url = f"{self.base_url}{endpoint}"
                
                self.log(f"測試: {endpoint} 參數: {params}")
                
                content, status, headers = self.make_request(url)
                
                if status == 200 and content:
                    try:
                        json_data = json.loads(content)
                        if json_data:  # 如果有實際資料
                            request_data = {
                                'endpoint': endpoint,
                                'params': params,
                                'url': url,
                                'status': status,
                                'json_data': json_data,
                                'timestamp': datetime.now().isoformat()
                            }
                            successful_requests.append(request_data)
                            self.discovered_apis.append(request_data)
                            self.log(f"✅ 成功獲取資料: {endpoint} 參數: {params}")
                    except:
                        pass
                
                time.sleep(0.3)
        
        return successful_requests
    
    def try_post_requests(self):
        """嘗試POST請求來獲取資料"""
        self.log("嘗試POST請求...")
        
        endpoints = [
            '/api/substrate/query',
            '/api/substrate/search',
            '/api/data/query',
            '/api/data/search',
            '/substrate/query',
            '/data/query'
        ]
        
        # POST資料組合
        post_data_combinations = [
            {
                'station': 'DF1',
                'start_date': '2025-06-09',
                'end_date': '2025-06-16'
            },
            {
                'stations': ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6'],
                'date_range': {
                    'start': '2025-06-09',
                    'end': '2025-06-16'
                }
            },
            {
                'query': {
                    'station': 'DF1',
                    'status': 'OK'
                }
            },
            {
                'filters': {
                    'station': 'DF1',
                    'type': 'AI_Loss'
                }
            }
        ]
        
        successful_posts = []
        
        for endpoint in endpoints:
            for post_data in post_data_combinations:
                url = f"{self.base_url}{endpoint}"
                self.log(f"POST測試: {endpoint}")
                
                content, status, headers = self.make_request(url, post_data, 'POST')
                
                if status == 200 and content:
                    try:
                        json_data = json.loads(content)
                        if json_data:
                            request_data = {
                                'endpoint': endpoint,
                                'method': 'POST',
                                'post_data': post_data,
                                'url': url,
                                'status': status,
                                'json_data': json_data,
                                'timestamp': datetime.now().isoformat()
                            }
                            successful_posts.append(request_data)
                            self.discovered_apis.append(request_data)
                            self.log(f"✅ POST成功: {endpoint}")
                    except:
                        pass
                
                time.sleep(0.5)
        
        return successful_posts
    
    def save_discoveries(self):
        """儲存發現的API資料"""
        if not self.discovered_apis:
            self.log("沒有發現任何API資料")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 儲存完整發現資料
        discovery_file = os.path.join(self.data_dir, f"api_discoveries_{timestamp}.json")
        with open(discovery_file, 'w', encoding='utf-8') as f:
            json.dump(self.discovered_apis, f, ensure_ascii=False, indent=2)
        self.log(f"API發現資料已儲存: {discovery_file}")
        
        # 儲存摘要
        summary = {
            'total_discoveries': len(self.discovered_apis),
            'successful_endpoints': [api['endpoint'] for api in self.discovered_apis if 'endpoint' in api],
            'json_endpoints': [api['endpoint'] for api in self.discovered_apis if api.get('is_json', False)],
            'timestamp': datetime.now().isoformat()
        }
        
        summary_file = os.path.join(self.data_dir, f"api_summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        self.log(f"API摘要已儲存: {summary_file}")
        
        # 生成報告
        report_file = os.path.join(self.data_dir, f"api_report_{timestamp}.html")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_html_report())
        self.log(f"API報告已儲存: {report_file}")
    
    def generate_html_report(self):
        """生成HTML報告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>API探索報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .api-section {{ border: 1px solid #ccc; margin: 20px 0; padding: 15px; }}
                .section-title {{ color: #333; border-bottom: 2px solid #007bff; }}
                .json-data {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; }}
                .success {{ color: green; }}
                .endpoint {{ font-weight: bold; color: #007bff; }}
                pre {{ white-space: pre-wrap; word-wrap: break-word; }}
            </style>
        </head>
        <body>
            <h1>API探索報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>共發現 {len(self.discovered_apis)} 個有效API端點</p>
        """
        
        for api in self.discovered_apis:
            html += f"""
            <div class="api-section">
                <h2 class="section-title">端點: <span class="endpoint">{api.get('endpoint', 'Unknown')}</span></h2>
                <p><strong>URL:</strong> {api.get('url', 'N/A')}</p>
                <p><strong>方法:</strong> {api.get('method', 'GET')}</p>
                <p><strong>狀態碼:</strong> <span class="success">{api.get('status', 'N/A')}</span></p>
                <p><strong>時間:</strong> {api.get('timestamp', 'N/A')}</p>
            """
            
            if api.get('params'):
                html += f"<p><strong>參數:</strong> {api['params']}</p>"
            
            if api.get('post_data'):
                html += f"<p><strong>POST資料:</strong> {api['post_data']}</p>"
            
            if api.get('json_data'):
                html += f"""
                <h3>JSON回應資料</h3>
                <div class="json-data">
                    <pre>{json.dumps(api['json_data'], ensure_ascii=False, indent=2)}</pre>
                </div>
                """
            
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        return html
    
    def run(self):
        """執行API探索"""
        self.log("開始API探索...")
        
        try:
            # 1. 分析JavaScript檔案
            js_endpoints = self.analyze_javascript()
            
            # 2. 探索常見API端點
            common_apis = self.explore_common_api_endpoints()
            
            # 3. 測試substrate端點的參數組合
            param_tests = self.test_substrate_endpoints_with_params()
            
            # 4. 嘗試POST請求
            post_tests = self.try_post_requests()
            
            # 5. 儲存發現的資料
            self.save_discoveries()
            
            self.log(f"API探索完成！共發現 {len(self.discovered_apis)} 個有效端點")
            return True
            
        except Exception as e:
            self.log(f"API探索過程中發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("API探索器 - PEGA AI系統")
    print("目標: 發現和測試API端點")
    print("=" * 60)
    
    explorer = APIExplorer()
    success = explorer.run()
    
    if success:
        print("\n✅ API探索完成！")
        print(f"📁 資料儲存在: {os.path.abspath(explorer.data_dir)}")
        print(f"📋 日誌儲存在: {os.path.abspath(explorer.log_dir)}")
    else:
        print("\n❌ API探索失敗")
