# Substrate Dashboard 爬蟲使用指南

## 概述

我為您建立了兩個版本的Substrate Dashboard爬蟲，專門用於：
1. 讀取 `input_data.csv` 中的特定資料 (如 a06198, 92540397)
2. 抓取三種圖片計數：OK Image Count, NG Image Count, Uncertain Image Count
3. 收集所有Station (DF1-DF6) 的詳細數據 (每個DF有9筆數據：OK/NG/SKIP)

## 🚀 快速開始

### 方案1: Requests-HTML 爬蟲 (推薦，無需WebDriver)

```bash
# 1. 安裝依賴
pip install requests-html pandas

# 2. 準備輸入資料
# 確保 input_data.csv 格式正確

# 3. 執行爬蟲
python requests_html_scraper.py
```

### 方案2: Selenium 爬蟲 (功能最完整)

```bash
# 1. 安裝依賴
pip install selenium pandas

# 2. 下載Chrome WebDriver (或使用webdriver-manager)
pip install webdriver-manager

# 3. 執行爬蟲
python substrate_dashboard_scraper.py
```

## 📁 檔案結構

### 輸入檔案
- `input_data.csv` - 包含要查詢的資料

### 輸出檔案
```
scraped_data_20250616/
├── dashboard_data/              # Selenium版本輸出
│   ├── dashboard_data_*.json
│   ├── dashboard_data_*.csv
│   └── summary_*.json
├── requests_html_data/          # Requests-HTML版本輸出
│   ├── requests_html_data_*.json
│   ├── requests_html_data_*.csv
│   └── report_*.html
└── logs/                        # 執行日誌
    ├── dashboard_scraper_*.log
    └── requests_html_scraper_*.log
```

## 📊 input_data.csv 格式

您的 `input_data.csv` 應該包含以下欄位：

```csv
code,lot,ship
A06-192,92540363,ship001
a06198,92540397,ship002
b07299,93651408,ship003
```

**欄位說明**:
- `code`: 產品代碼 (如 a06198, A06-192)
- `lot`: 批次號碼 (如 92540397, 92540363)
- `ship`: 出貨資訊 (可選)

## 🎯 抓取的資料結構

### 1. 圖片計數 (Image Counts)
```json
{
  "image_counts": {
    "OK_Image_Count": "788",
    "NG_Image_Count": "2,836", 
    "Uncertain_Image_Count": "1,204"
  }
}
```

### 2. Station詳細資料 (每個DF有9筆數據)
```json
{
  "station_details": {
    "DF1": {
      "OK": ["數值1", "數值2", "數值3"],
      "NG": ["數值1", "數值2", "數值3"],
      "SKIP": ["數值1", "數值2", "數值3"]
    },
    "DF2": { ... },
    "DF3": { ... },
    "DF4": { ... },
    "DF5": { ... },
    "DF6": { ... }
  }
}
```

## 🔧 自訂設定

### 修改Station列表
在爬蟲檔案中找到：
```python
self.stations = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6']
```

### 修改登入資訊
```python
self.username = 'admin'
self.password = 'pega#1234'
self.base_url = 'http://************:31539'
```

### 調整等待時間
```python
time.sleep(3)  # 增加或減少等待時間
```

## 📋 輸出資料格式

### CSV格式
| input_code | input_lot | input_ship | timestamp | ok_image_count | ng_image_count | uncertain_image_count | station | station_ok | station_ng | station_skip |
|------------|-----------|------------|-----------|----------------|----------------|----------------------|---------|------------|------------|--------------|
| A06-192 | 92540363 | ship001 | 2025-06-16T... | 788 | 2,836 | 1,204 | DF1 | 數值1,數值2,數值3 | 數值1,數值2,數值3 | 數值1,數值2,數值3 |

### JSON格式
```json
[
  {
    "input_data": {
      "code": "A06-192",
      "lot": "92540363",
      "ship": "ship001"
    },
    "timestamp": "2025-06-16T18:30:00",
    "image_counts": {
      "OK_Image_Count": "788",
      "NG_Image_Count": "2,836",
      "Uncertain_Image_Count": "1,204"
    },
    "station_details": {
      "DF1": {
        "OK": ["數值1", "數值2", "數值3"],
        "NG": ["數值1", "數值2", "數值3"],
        "SKIP": ["數值1", "數值2", "數值3"]
      }
    }
  }
]
```

## 🚨 常見問題

### 1. 無法安裝Chrome WebDriver
**解決方案**: 使用 requests-html 版本
```bash
pip install requests-html
python requests_html_scraper.py
```

### 2. 網頁載入緩慢
**解決方案**: 增加等待時間
```python
time.sleep(5)  # 增加到5秒
r.html.render(wait=5)  # 增加渲染等待時間
```

### 3. 找不到特定元素
**解決方案**: 檢查網頁結構是否改變，修改XPath選擇器

### 4. 資料格式不正確
**解決方案**: 檢查 input_data.csv 格式，確保有正確的標題行

## 🔍 除錯技巧

### 1. 檢查日誌檔案
```bash
# 查看最新的日誌
tail -f scraped_data_20250616/logs/requests_html_scraper_*.log
```

### 2. 檢查網頁內容
在爬蟲中添加：
```python
print(r.html.html[:1000])  # 印出前1000個字元
```

### 3. 手動測試
```python
# 在Python中手動測試
from requests_html import HTMLSession
session = HTMLSession()
r = session.get('http://************:31539/substrate/overall')
r.html.render(wait=3)
print(r.html.text)
```

## 📈 效能優化

### 1. 批次處理
```python
# 每處理5筆資料後休息
if i % 5 == 0:
    time.sleep(10)
```

### 2. 並行處理 (進階)
```python
from concurrent.futures import ThreadPoolExecutor
# 使用多執行緒處理多筆資料
```

### 3. 快取機制
```python
# 儲存已處理的資料，避免重複抓取
processed_codes = set()
```

## 🎯 下一步建議

1. **先測試 requests-html 版本** - 最簡單，無需額外下載
2. **檢查輸出資料** - 確認格式符合需求
3. **調整參數** - 根據實際情況修改等待時間
4. **擴展功能** - 添加更多篩選條件或資料處理

## 📞 技術支援

如果遇到問題：
1. 檢查日誌檔案中的錯誤訊息
2. 確認網站是否正常運作
3. 驗證 input_data.csv 格式
4. 測試網路連線

---

**建議執行順序**:
1. 確認 `input_data.csv` 格式正確
2. 執行 `python requests_html_scraper.py`
3. 檢查輸出檔案
4. 如需更多功能，再使用 Selenium 版本
