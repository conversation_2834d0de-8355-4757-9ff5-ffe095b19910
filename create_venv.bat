@echo off
echo Checking for Python...

where python >nul 2>nul
if %errorlevel% equ 0 (
    echo Python found!
    echo Creating virtual environment...
    python -m venv venv
) else (
    where py >nul 2>nul
    if %errorlevel% equ 0 (
        echo Python launcher found!
        echo Creating virtual environment...
        py -m venv venv
    ) else (
        echo Python not found. Please install Python and try again.
        goto end
    )
)

if exist venv (
    echo.
    echo Virtual environment created successfully!
    echo.
    echo To activate the virtual environment:
    echo - In Command Prompt: venv\Scripts\activate.bat
    echo - In PowerShell: venv\Scripts\Activate.ps1
    echo.
    echo To install required packages (after activation):
    echo pip install -r requirements.txt
    echo.
    echo To deactivate the virtual environment:
    echo deactivate
) else (
    echo Failed to create virtual environment.
)

:end
pause
