#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("開始測試...")

try:
    print("1. 測試基本模組...")
    import pandas as pd
    print("✅ pandas 正常")
    
    import json
    print("✅ json 正常")
    
    import urllib.request
    print("✅ urllib 正常")
    
    print("2. 測試 requests-html...")
    from requests_html import HTMLSession
    print("✅ requests-html 載入成功")
    
    print("3. 測試基本連接...")
    session = HTMLSession()
    print("✅ HTMLSession 建立成功")
    
    print("4. 測試網站連接...")
    r = session.get('http://10.22.22.144:31539', timeout=10)
    print(f"✅ 網站連接成功，狀態碼: {r.status_code}")
    
    print("5. 測試 input_data.csv...")
    import os
    if os.path.exists('input_data.csv'):
        df = pd.read_csv('input_data.csv')
        print(f"✅ input_data.csv 載入成功，{len(df)} 筆資料")
        print("資料內容:")
        print(df)
    else:
        print("❌ 找不到 input_data.csv")
    
    print("\n🎉 所有基本測試通過！")
    
except Exception as e:
    print(f"❌ 測試失敗: {str(e)}")
    import traceback
    traceback.print_exc()

print("測試完成")
